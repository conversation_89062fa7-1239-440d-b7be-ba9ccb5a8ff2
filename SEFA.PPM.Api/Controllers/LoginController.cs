using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.PPM.Controllers;
using System.Text;

namespace SEFA.PPM.Api.Controllers
{
    [Produces("application/json")]
    [Route("api/Login")]
    [Authorize(Permissions.Name)]
    public class LoginController : BaseApiController
    {


        /// <summary>
        /// 构造函数注入
        /// </summary>
        public LoginController()
        {

        }
        [HttpPost]
        [Route("/api/Login/swgLogin")]
        [AllowAnonymous]
        public dynamic SwgLogin([FromBody] SwaggerLoginRequest loginRequest)
        {
            byte[] decodedBytes = Convert.FromBase64String(loginRequest?.pwd);
            var pwd = Encoding.UTF8.GetString(decodedBytes);
            // 这里可以查询数据库等各种校验
            if (loginRequest?.name == "admin" && pwd == "lkkxh123B")
            {
                HttpContext.Session.SetString("swagger-code", "success");
                return new { result = true };
            }

            return new { result = false };
        }



        public class SwaggerLoginRequest
        {
            public string name { get; set; }
            public string pwd { get; set; }
        }



    }
}

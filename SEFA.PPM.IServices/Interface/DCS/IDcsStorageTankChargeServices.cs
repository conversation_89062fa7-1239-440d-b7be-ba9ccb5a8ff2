using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsStorageTankChargeServices
	/// </summary>	
    public interface IDcsStorageTankChargeServices :IBaseServices<DcsStorageTankChargeEntity>
	{
		Task<PageModel<DcsStorageTankChargeEntity>> GetPageList(DcsStorageTankChargeRequestModel reqModel);

        Task<List<DcsStorageTankChargeEntity>> GetList(DcsStorageTankChargeRequestModel reqModel);

		Task<bool> SaveForm(DcsStorageTankChargeEntity entity);
    }
}
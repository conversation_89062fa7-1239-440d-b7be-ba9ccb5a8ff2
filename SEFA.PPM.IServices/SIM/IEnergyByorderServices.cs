using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.ViewModels.SIM;
using SEFA.PPM.Model.Models.SIM;

namespace SEFA.PPM.IServices.SIM
{
    /// <summary>
    /// IEnergyByorderServices
    /// </summary>	
    public interface IEnergyByorderServices : IBaseServices<EnergyByorderEntity>
    {
        Task<PageModel<EnergyByorderEntity>> GetPageList(EnergyByorderRequestModel reqModel);

        Task<List<EnergyByorderEntity>> GetList(EnergyByorderRequestModel reqModel);

        Task<bool> SaveForm(EnergyByorderEntity entity);
    }
}
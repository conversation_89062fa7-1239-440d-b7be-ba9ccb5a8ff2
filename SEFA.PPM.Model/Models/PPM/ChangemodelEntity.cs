using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_M_ChangeModel")] 
    public class ChangemodelEntity : EntityBase
    {
        public ChangemodelEntity()
        {
        }
           /// <summary>
           /// Desc:生产线ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:生产线代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LINE_CODE")]
        public string LineCode { get; set; }
           /// <summary>
           /// Desc:生产线名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LINE_NAME")]
        public string LineName { get; set; }
           /// <summary>
           /// Desc:前容器类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRESALESCONTAINER")]
        public string Presalescontainer { get; set; }
           /// <summary>
           /// Desc:前容器代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRESALESCONTAINER_DESCRIPTION")]
        public string PresalescontainerDescription { get; set; }
           /// <summary>
           /// Desc:后容器类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SALESCONTAINER")]
        public string Salescontainer { get; set; }
           /// <summary>
           /// Desc:后容器代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SALESCONTAINER_DESCRIPTION")]
        public string SalescontainerDescription { get; set; }
           /// <summary>
           /// Desc:换型时间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ChangeTime")]
        public decimal Changetime { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:换型模式
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CHANGE_MODEL")]
        public string ChangeModel { get; set; }

    }
}
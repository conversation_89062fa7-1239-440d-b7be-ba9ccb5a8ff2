using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class SampleListController : BaseApiController
    {
        /// <summary>
        /// SampleList
        /// </summary>
        private readonly ISampleListServices _sampleListServices;

        public SampleListController(ISampleListServices SampleListServices)
        {
            _sampleListServices = SampleListServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<SampleListEntity>>> GetList([FromBody] SampleListRequestModel reqModel)
        {
            var data = await _sampleListServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SampleListEntity>>> GetPageList([FromBody] SampleListRequestModel reqModel)
        {
            var data = await _sampleListServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SampleListEntity>> GetEntity(string id)
        {
            var data = await _sampleListServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 扫描设备铭牌，返回设备及设备当前运行工单的部分信息
        /// </summary>
        /// <param name="sampleEquipment"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<EquipmentAndOrderEntity>> ScanSampleEquipment([FromBody] string sampleEquipment)
        {
            return await _sampleListServices.ScanSampleEquipment(sampleEquipment);
        }

        /// <summary>
        /// 扫描容器编码，返回容器
        /// </summary>
        /// <param name="containerCode"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<SampleListEntity>> ScanContainerCode([FromBody] string containerCode)
        {
            return await _sampleListServices.ScanContainerCode(containerCode);
        }

        /// <summary>
        /// check当前设备是否存在待填写对应取样类型的表单，
        /// 如果不存在则直接创建表单并关联容器；
        /// 如存在待填写且取样类型为正常取样，则返回是否需要引用该表单
        /// 如存在待填写且取样类型不为正常取样，则直接返回失败
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<LogsheetEntity>> CheckLogSheet([FromBody] EquipmentAndOrderEntity reqModel)
        {
            return await _sampleListServices.CheckLogSheet(reqModel);
        }

        /// <summary>
        /// check结果为存在时需要重新选择是否引用当前已存在表单在提交
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveSampling([FromBody] EquipmentAndOrderEntity reqModel)
        {
            return await _sampleListServices.SaveSampling(reqModel);
        }

        /// <summary>
        /// 修改容器状态
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateStatus([FromBody] SampleListEntity reqModel)
        {
            return await _sampleListServices.UpdateStatus(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SampleListEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _sampleListServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _sampleListServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] SampleListEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _sampleListServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sampleListServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> StartDetection([FromBody] string[] ids)
        {
            return await _sampleListServices.StartDetection(ids);
        }

        [HttpPost]
        public async Task<MessageModel<string>> AddSampling()
        {
            return await _sampleListServices.AddSampling();
        }
    }
    //public class SampleListRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}
using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PTM_B_DCS_CHARGE_REQUEST")] 
    public class DcsChargeRequestEntity : EntityBase
    {
        public DcsChargeRequestEntity()
        {
        }
           /// <summary>
           /// Desc:工单
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:设备
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:OPID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OP_ID")]
        public string OpId { get; set; }
           /// <summary>
           /// Desc:Op名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OP_NAME")]
        public string OpName { get; set; }
           /// <summary>
           /// Desc:投料口
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="INPUT_MATERIAL_EQU_CODE")]
        public string InputMaterialEquCode { get; set; }
           /// <summary>
           /// Desc:物料名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_NAME")]
        public string MaterialName { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:OP状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OP_STATE")]
        public string OpState { get; set; }

        /// <summary>
        /// Desc:处理状态 1:处理中,0处理完成
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STATE")]
        public string State { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

    }
}
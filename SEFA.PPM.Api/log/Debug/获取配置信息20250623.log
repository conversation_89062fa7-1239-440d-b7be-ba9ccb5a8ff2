2025-06-23 14:46:25.475 +08:00 [DBG] 调用DFM接口
2025-06-23 14:47:34.009 +08:00 [DBG] 调用DFM接口
2025-06-23 14:47:34.429 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:47:34.430 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:47:34.446 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:47:34.446 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:47:34.503 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:47:34.504 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:48:12.126 +08:00 [DBG] 调用DFM接口
2025-06-23 14:48:12.373 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:48:12.391 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:48:12.440 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:48:14.399 +08:00 [DBG] 调用DFM接口
2025-06-23 14:48:14.808 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:48:14.827 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:48:14.890 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:48:21.967 +08:00 [DBG] 调用DFM接口
2025-06-23 14:48:22.347 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:48:22.365 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:48:22.478 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:48:40.357 +08:00 [DBG] 调用DFM接口
2025-06-23 14:48:40.603 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:48:40.619 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:48:40.686 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:58:15.087 +08:00 [DBG] 调用DFM接口
2025-06-23 14:58:15.330 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:58:15.342 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:58:15.391 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:58:30.113 +08:00 [DBG] 调用DFM接口
2025-06-23 14:58:30.382 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:58:30.404 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:58:30.492 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 14:59:39.712 +08:00 [DBG] 调用DFM接口
2025-06-23 14:59:39.954 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 14:59:39.963 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 14:59:40.032 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 15:19:58.086 +08:00 [DBG] 调用DFM接口
2025-06-23 15:19:58.345 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 15:19:58.357 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 15:19:58.473 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 15:20:02.492 +08:00 [DBG] 调用DFM接口
2025-06-23 15:20:02.754 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 15:20:02.767 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 15:20:02.812 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 15:20:19.591 +08:00 [DBG] 调用DFM接口
2025-06-23 15:20:19.840 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 15:20:19.852 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 15:20:20.013 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:16:17.437 +08:00 [DBG] 调用DFM接口
2025-06-23 16:16:17.742 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:16:17.768 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:16:17.845 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:16:40.220 +08:00 [DBG] 调用DFM接口
2025-06-23 16:16:40.565 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:16:40.581 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:16:40.646 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:33:46.103 +08:00 [DBG] 调用DFM接口
2025-06-23 16:33:46.536 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:33:46.545 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-23 16:35:05.814 +08:00 [DBG] 调用DFM接口
2025-06-23 16:35:06.055 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:35:06.071 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:35:06.131 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:35:10.070 +08:00 [DBG] 调用DFM接口
2025-06-23 16:35:10.311 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:35:10.321 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:35:10.372 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:35:14.264 +08:00 [DBG] 调用DFM接口
2025-06-23 16:35:14.543 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:35:14.551 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:35:14.596 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:35:57.756 +08:00 [DBG] 调用DFM接口
2025-06-23 16:35:57.998 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:35:58.040 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:35:58.183 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:36:00.065 +08:00 [DBG] 调用DFM接口
2025-06-23 16:36:00.365 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:36:00.376 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:36:00.420 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:36:27.678 +08:00 [DBG] 调用DFM接口
2025-06-23 16:36:27.996 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:36:28.008 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:36:28.058 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:36:47.632 +08:00 [DBG] 调用DFM接口
2025-06-23 16:36:47.982 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:36:48.000 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:36:48.042 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:36:50.607 +08:00 [DBG] 调用DFM接口
2025-06-23 16:36:50.870 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:36:50.885 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-23 16:36:56.768 +08:00 [DBG] 调用DFM接口
2025-06-23 16:36:56.992 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:36:57.010 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:36:57.075 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 16:40:44.658 +08:00 [DBG] 调用DFM接口
2025-06-23 16:40:45.061 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 16:40:45.081 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 16:40:45.239 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条
2025-06-23 23:02:47.491 +08:00 [DBG] 调用DFM接口
2025-06-23 23:02:47.682 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-23 23:02:47.706 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintTemplete]值[LKK_INVENTORY_LABEL]
2025-06-23 23:02:47.790 +08:00 [DBG] 未查询设备,返回[LKK_INVENTORY_LABEL]数据[0]条

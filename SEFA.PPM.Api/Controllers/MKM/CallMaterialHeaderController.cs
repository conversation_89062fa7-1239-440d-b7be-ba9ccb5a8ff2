using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class CallMaterialHeaderController : BaseApiController
    {
        /// <summary>
        /// CallMaterialHeader
        /// </summary>
        private readonly ICallMaterialHeaderServices _callMaterialHeaderServices;

        private readonly ICallMaterialDetailServices _callMaterialDetailServices;

        public CallMaterialHeaderController(ICallMaterialHeaderServices CallMaterialHeaderServices,
            ICallMaterialDetailServices callMaterialDetailServices)
        {
            _callMaterialHeaderServices = CallMaterialHeaderServices;
            _callMaterialDetailServices = callMaterialDetailServices;
        }

        /// <summary>
        /// 获取叫料单主表列表
        /// </summary>
        /// <param name="reqModel">查询条件</param>
        /// <returns>叫料单主表列表</returns>
        [HttpPost]
        public async Task<MessageModel<List<CallMaterialHeaderEntity>>> GetList(
            [FromBody] CallMaterialHeaderRequestModel reqModel)
        {
            var data = await _callMaterialHeaderServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取叫料单主表分页列表
        /// </summary>
        /// <param name="reqModel">查询条件</param>
        /// <returns>叫料单主表分页列表</returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<CallMaterialHeaderView>>> GetPageList(
            [FromBody] CallMaterialHeaderRequestModel reqModel)
        {
            var data = await _callMaterialHeaderServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据ID获取叫料单主表信息
        /// </summary>
        /// <param name="id">叫料单主表ID</param>
        /// <returns>叫料单主表信息</returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<CallMaterialHeaderEntity>> GetEntity(string id)
        {
            var data = await _callMaterialHeaderServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 保存叫料单主表信息
        /// </summary>
        /// <param name="request">叫料单主表信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] CallMaterialHeaderEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _callMaterialHeaderServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _callMaterialHeaderServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        /// <summary>
        /// 新增叫料单主表信息
        /// </summary>
        /// <param name="request">叫料单主表信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] CallMaterialHeaderEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _callMaterialHeaderServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        /// <summary>
        /// 删除叫料单主表信息
        /// </summary>
        /// <param name="ids">叫料单主表ID数组</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _callMaterialHeaderServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 保存叫料单主表及子表信息
        /// </summary>
        /// <param name="request">叫料单主表及明细信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveCallMaterialForm([FromBody] CallMaterialFormRequest request)
        {
            var result = await _callMaterialHeaderServices.SaveCallMaterialForm(request.Header, request.Details);
            if (result.success)
            {
                return Success(result.response, result.msg);
            }
            else
            {
                return Failed(result.msg);
            }
        }

        /// <summary>
        /// 根据ID获取叫料单主表及子表信息
        /// </summary>
        /// <param name="workOrderId">主表ID</param>
        /// <returns>叫料单主表及子表信息</returns>
        [HttpGet("{workOrderNo}/with-details")]
        public async Task<MessageModel<CallMaterialHeaderView>> GetCallMaterialWithDetails(string workOrderId)
        {
            var headerResult = await _callMaterialHeaderServices.GetCallMaterialWithDetails(workOrderId);
            return headerResult;
        }

        /// <summary>
        /// 删除叫料单主表及子表信息
        /// </summary>
        /// <param name="ids">主表ID数组</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteCallMaterialForm([FromBody] string[] ids)
        {
            var result = await _callMaterialHeaderServices.DeleteCallMaterialForm(ids);
            if (result.success)
            {
                return Success("", result.msg);
            }
            else
            {
                return Failed(result.msg);
            }
        }
    }

    /// <summary>
    /// 叫料单表单请求
    /// </summary>
    public class CallMaterialFormRequest
    {
        /// <summary>
        /// 叫料单主表
        /// </summary>
        public CallMaterialHeaderEntity Header { get; set; }

        /// <summary>
        /// 叫料单明细
        /// </summary>
        public List<CallMaterialDetailEntity> Details { get; set; }
    }

    /// <summary>
    /// 叫料单表单响应
    /// </summary>
    public class CallMaterialFormResponse
    {
        /// <summary>
        /// 叫料单主表
        /// </summary>
        public CallMaterialHeaderView Header { get; set; }

        /// <summary>
        /// 叫料单明细
        /// </summary>
        public List<CallMaterialDetailEntity> Details { get; set; }
    }
}
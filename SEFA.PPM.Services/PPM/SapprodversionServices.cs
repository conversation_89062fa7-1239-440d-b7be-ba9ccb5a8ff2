
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.ESB;
using SEFA.PPM.Model.Models.Interface;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.Common;
using Dm.parser;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Drawing;
using System.Numerics;
using SEFA.DFM.Model.Models;
using System.Linq;
using SEFA.Base.Common.LogHelper;
using System.Text.Json;

namespace SEFA.PPM.Services
{
    /// <summary>
    /// Sap ProdVersion
    /// </summary>
    public class SapprodversionServices : BaseServices<SapprodversionEntity>, ISapprodversionServices
    {
        private readonly IBaseRepository<SapprodversionEntity> _dal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        private readonly IBaseRepository<MaterialVersionEntity> _materialVersionDal;
        private readonly LKKESBHelper _lkKESBHelper;
        private readonly IUnitOfWork _unitOfWork;
        public SapprodversionServices(IBaseRepository<SapprodversionEntity> dal,
            IBaseRepository<MaterialEntity> ma,
            IBaseRepository<MaterialVersionEntity> mv,
            LKKESBHelper lkKESBHelper,
            IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._materialDal = ma;
            this._materialVersionDal = mv;
            this._lkKESBHelper = lkKESBHelper;
            this._unitOfWork = unitOfWork;
        }

        public async Task<List<SapprodversionEntity>> GetList(SapprodversionRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SapprodversionEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.Matnr), p => p.Matnr == reqModel.Matnr)
                             .AndIF(!string.IsNullOrEmpty(reqModel.Verid), p => p.Verid == reqModel.Verid)
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);

            var codeList = data.Select(a => a.Matnr).Distinct();

            var mtrInfo = await _materialDal.FindList(p => p.Deleted == 0 && !string.IsNullOrEmpty(p.Description) && codeList.Contains(p.Code));
            foreach (var item in data)
            {
                var minfo = mtrInfo.FirstOrDefault(p => p.Code == item.Matnr);
                if (minfo != null)
                {
                    item.SapFormula = minfo.Description;
                }
            }
            return data;
        }

        public async Task<PageModel<SapprodversionEntity>> GetPageList(SapprodversionRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SapprodversionEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.Matnr), p => p.Matnr == reqModel.Matnr)
                             .AndIF(!string.IsNullOrEmpty(reqModel.Verid), p => p.Verid == reqModel.Verid)
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            var mtrInfo = await _materialDal.FindList(p => p.Deleted == 0 && !string.IsNullOrEmpty(p.Description));
            foreach (var item in data.data)
            {
                var minfo = mtrInfo.FirstOrDefault(p => p.Code == item.Matnr);
                if (minfo != null)
                {
                    item.SapFormula = minfo.Description;
                }
            }

            return data;
        }

        public async Task<bool> SaveForm(SapprodversionEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 获取SAP ProdVersion
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> GetSapProdVersion(string factory)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            var req = new
            {
                MATNR = "",
                TEXT1 = "",
                WERKS = factory,  //工厂
                VERID = ""
            };
            var responsData = await _lkKESBHelper.PostJson<ESBBaseModel<List<PP_SAP_ProdVersion>>, dynamic>("SAP_PRODVERSIONGET", req, null);
            if (responsData.successed == true)
            {
                var dataList = responsData.Response.data;
                SerilogServer.LogDebug( JsonSerializer.Serialize(dataList), "GetSapProdVersion接收数据");
                _unitOfWork.BeginTran();
                try
                {
                    foreach (var data in dataList)
                    {
                        var list = await _dal.FindList(p => p.Matnr == data.MATNR && p.Werks == data.WERKS && p.Verid == data.VERID);
                        if (list.Count > 1)
                        {
                            await _dal.Delete(p => p.Matnr == data.MATNR && p.Werks == data.WERKS && p.Verid == data.VERID);
                        }
                        SapprodversionEntity obj = await _dal.FindEntity(p => p.Matnr == data.MATNR && p.Werks == data.WERKS && p.Verid == data.VERID);
                        if (obj == null)
                        {
                            obj = new SapprodversionEntity()
                            {
                                Matnr = data.MATNR,
                                Werks = data.WERKS,
                                Verid = data.VERID,
                                Adatu = data.ADATU,
                                Alnal = data.ALNAL,
                                Bdatu = data.BDATU,
                                Bstma = data.BSTMA,
                                Bstmi = data.BSTMI,
                                Plnnr = data.PLNNR,
                                Plnty = data.PLNTY,
                                Stlal = data.STLAL,
                                Stlan = data.STLAN,
                                Text1 = data.TEXT1,
                                Arbpl = data.ARBPL,
                                Ktext = data.KTEXT,
                                SapCreatedate = data.Createdate
                            };
                            obj.CreateCustomGuid("SAP_PRODVERSIONGET");
                            await _dal.Add(obj);
                        }
                        else
                        {
                            obj.Adatu = data.ADATU;
                            obj.Alnal = data.ALNAL;
                            obj.Bdatu = data.BDATU;
                            obj.Bstma = data.BSTMA;
                            obj.Bstmi = data.BSTMI;
                            obj.Plnnr = data.PLNNR;
                            obj.Plnty = data.PLNTY;
                            obj.Stlal = data.STLAL;
                            obj.Stlan = data.STLAN;
                            obj.Text1 = data.TEXT1;
                            obj.Arbpl = data.ARBPL;
                            obj.Ktext = data.KTEXT;
                            obj.SapCreatedate = data.Createdate;
                            obj.Modify(obj.ID, "SAP_PRODVERSIONGET");
                            await _dal.Update(obj);
                        }

                        var mtrInfo = await _materialDal.FindEntity(p => p.Code == data.MATNR);
                        if (mtrInfo != null)
                        {
                            var mvinfo = await _materialVersionDal.FindEntity(p => p.MaterialId == mtrInfo.ID && p.MaterialVersionNumber == data.VERID);
                            if (mvinfo == null)
                            {
                                mvinfo = new MaterialVersionEntity
                                {
                                    MaterialId = mtrInfo.ID,
                                    MaterialVersionNumber = data.VERID,
                                    Plantcode = data.WERKS,
                                    Remark = data.TEXT1
                                };
                                mvinfo.CreateCustomGuid("SAP_PRODVERSIONGET");
                                await _materialVersionDal.Add(mvinfo);
                            }
                        }
                    }
                    _unitOfWork.CommitTran();
                    // var r = await _lkKESBHelper.PostJson<string, FlagUpdateModel>("SAP_FlagUpdate", new FlagUpdateModel(responsData.Response.dataType, responsData.Response.ids));
                    result.msg = "操作成功";
                    result.success = true;
                }
                catch (Exception ex)
                {
                    result.msg = "操作失败";
                    result.msgDev = ex.Message;
                    _unitOfWork.RollbackTran();
                }
            }
            else
            {
                result.msgDev = responsData.msg;
            }
            return result;
        }

        /// <summary>
        /// 获取SAP ProdVersion
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> GetSapProdVersionAsync(string factory)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            var req = new
            {
                MATNR = "",
                TEXT1 = "",
                WERKS = factory,  //工厂
                VERID = ""
            };
            var responsData = await _lkKESBHelper.PostJsonAsync<ESBBaseModel<string>, dynamic>("SAP_PRODVERSIONGET", req, null);

            if (responsData.successed == true)
            {
                result.msg = responsData.msg;
            }
            else
            {
                result.msg = "调用失败:" + responsData.msg;
            }
            return result;
        }
    }
}
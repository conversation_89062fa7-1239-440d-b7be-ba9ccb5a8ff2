using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsBatchStatusServices
	/// </summary>	
    public interface IDcsBatchStatusServices :IBaseServices<DcsBatchStatusEntity>
	{
		Task<PageModel<DcsBatchStatusEntity>> GetPageList(DcsBatchStatusRequestModel reqModel);

        Task<List<DcsBatchStatusEntity>> GetList(DcsBatchStatusRequestModel reqModel);

		Task<bool> SaveForm(DcsBatchStatusEntity entity);
    }
}
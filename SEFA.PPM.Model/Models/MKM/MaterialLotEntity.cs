using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("MKM_B_MATERIAL_LOT")]
    public class MaterialLotEntity : EntityBase
    {
        public MaterialLotEntity()
        {
        }
        /// <summary>
        /// Desc:物料批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOT_ID")]
        public string LotId { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:有效期
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EXPIRATION_DATE")]
        public DateTime ExpirationDate { get; set; }
        /// <summary>
        /// Desc:生产日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_DATE_LOCAL")]
        public DateTime? ProductionDateLocal { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public string Type { get; set; }
        /// <summary>
        /// Desc:状态1:R 2:U
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EXTERNAL_STATUS")]
        public string ExternalStatus { get; set; }
        /// <summary>
        /// Desc:物料处置ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DISPOSITION_ID")]
        public string DispositionId { get; set; }
        /// <summary>
        /// Desc:供应商CODE
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUPPIERCODE")]
        public string Suppiercode { get; set; }
        /// <summary>
        /// Desc:供应商名字
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUPPIERNAME")]
        public string Suppiername { get; set; }
    }
}
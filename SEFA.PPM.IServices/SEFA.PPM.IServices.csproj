<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\SEFA.PPM.Model\SEFA.PPM.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="WebApiClients\HttpApis\" />
    <Folder Include="Report\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="SEFA.Base.Common">
      <HintPath>..\..\common\SEFA.Base.Common.dll</HintPath>
    </Reference>
    <Reference Include="SEFA.Base.ESB">
      <HintPath>..\..\common\SEFA.Base.ESB.dll</HintPath>
    </Reference>
    <Reference Include="SEFA.Base.IServices">
      <HintPath>..\..\common\SEFA.Base.IServices.dll</HintPath>
    </Reference>
    <Reference Include="SEFA.Base.Model">
      <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
    </Reference>
    <Reference Include="SEFA.DFM.Model">
      <HintPath>..\..\common\SEFA.DFM.Model.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>

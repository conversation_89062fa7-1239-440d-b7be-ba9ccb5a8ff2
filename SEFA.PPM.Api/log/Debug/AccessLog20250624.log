{"User":"","IP":"::ffff:127.0.0.1","API":"/api/container/getdestinationlist_yl","BeginTime":"2025-06-24 11:42:42","OPTime":"21ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getcontainerselectlist","BeginTime":"2025-06-24 11:42:43","OPTime":"16ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like <PERSON>ecko) Chrome/********* Safari/537.36"},
{"User":"admin","IP":"::ffff:127.0.0.1","API":"/api/container/getdestinationlist_yl","BeginTime":"2025-06-24 11:42:42","OPTime":"70ms","RequestMethod":"POST","RequestData":"{}","Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"admin","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getcontainerselectlist","BeginTime":"2025-06-24 11:42:43","OPTime":"7ms","RequestMethod":"GET","RequestData":"","Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"","IP":"::ffff:127.0.0.1","API":"/api/inventorylistingview/getinventlist_yl","BeginTime":"2025-06-24 11:42:43","OPTime":"18ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getselectprinit_bag","BeginTime":"2025-06-24 11:42:43","OPTime":"18ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getmselectlistbyclass","BeginTime":"2025-06-24 11:42:43","OPTime":"6ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"admin","IP":"::ffff:127.0.0.1","API":"/api/inventorylistingview/getinventlist_yl","BeginTime":"2025-06-24 11:42:43","OPTime":"57ms","RequestMethod":"POST","RequestData":"{\"typeRoom\":\"Invent\",\"Material\":\"\",\"Batch\":\"\",\"Sscc\":\"\",\"Statusf\":\"\",\"Statuss\":\"\",\"Location\":\"\",\"Bin\":\"\",\"Container\":\"\",\"ProOrderNo\":\"\",\"Bucketnum\":\"\",\"Formula\":\"\",\"typeSearch\":\"\",\"pageIndex\":1,\"pageSize\":20,\"StartTime\":\"2025-06-17\",\"EndTime\":\"2025-06-24 23:59:59\",\"orderByFileds\":\"\"}","Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"admin","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getmselectlistbyclass","BeginTime":"2025-06-24 11:42:43","OPTime":"57ms","RequestMethod":"GET","RequestData":"?type=","Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
{"User":"admin","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getselectprinit_bag","BeginTime":"2025-06-24 11:42:43","OPTime":"57ms","RequestMethod":"POST","RequestData":"{}","Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},

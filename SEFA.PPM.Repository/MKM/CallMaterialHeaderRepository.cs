using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.IRepository;
using SEFA.PPM.Model.Models;

namespace SEFA.PPM.Repository
{
    /// <summary>
    /// CallMaterialHeaderRepository
    /// </summary>
    public class CallMaterialHeaderRepository : BaseRepository<CallMaterialHeaderEntity>, ICallMaterialHeaderRepository
    {
        public CallMaterialHeaderRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}
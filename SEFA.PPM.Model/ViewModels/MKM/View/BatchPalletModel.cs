using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels.View;

public class BatchPalletModel: RequestPageModelBase
{
    public string typeSerch { get; set; }

    public string LocationF { get; set; }
    /// <summary>
    /// Desc:
    /// Default:
    /// Nullable:True
    /// </summary>
    public string LocationS { get; set; }
    /// <summary>
    /// Desc:
    /// Default:
    /// Nullable:True
    /// </summary>
    public string ProOrder { get; set; }
    /// <summary>
    /// Desc:
    /// Default:
    /// Nullable:True
    /// </summary>
    public string CMachine { get; set; }
    /// <summary>
    /// Desc:
    /// Default:
    /// Nullable:True
    /// </summary>
    public string BNubmber { get; set; }

    /// <summary>
    /// 容器状态
    /// </summary>
    public string ConStatus { get; set; }
    
         public string StartTime { get; set; }
    public string  StarTime { get; set; }


    public string EndTime { get; set; }
	/// <summary>
	/// Desc:
	/// Default:
	/// Nullable:True
	/// </summary>
	public string Formula { get; set; }

}
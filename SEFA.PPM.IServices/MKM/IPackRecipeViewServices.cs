using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackRecipeViewServices
	/// </summary>	
    public interface IPackRecipeViewServices :IBaseServices<PackRecipeViewEntity>
	{
		Task<PageModel<PackRecipeViewEntity>> GetPageList(PackRecipeViewRequestModel reqModel);

        Task<List<PackRecipeViewEntity>> GetList(PackRecipeViewRequestModel reqModel);

		Task<bool> SaveForm(PackRecipeViewEntity entity);
    }
}
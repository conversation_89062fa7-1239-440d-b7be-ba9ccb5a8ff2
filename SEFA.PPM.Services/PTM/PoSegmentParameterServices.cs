
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.Services
{
    public class PoSegmentParameterServices : BaseServices<PoSegmentParameterEntity>, IPoSegmentParameterServices
    {
        private readonly IBaseRepository<PoSegmentParameterEntity> _dal;
        public PoSegmentParameterServices(IBaseRepository<PoSegmentParameterEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<PoSegmentParameterEntity>> GetList(PoSegmentParameterRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PoSegmentParameterEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }


        /// <summary>
        /// 根据工单号获取工单的工序参数信息
        /// </summary>
        /// <param name="poId">工单ID</param>
        /// <returns></returns>
        public async Task<PageModel<PoSegmentParameterEntity>> GetPoSegmentParameterList(PoSegmentParameterRequestModel model)
        {
            RefAsync<int> count = 0;
            var data = new PageModel<PoSegmentParameterEntity>();
            var list = await _dal.Db.Queryable<ProductionOrderEntity, PoSegmentParameterEntity, EquipmentEntity>((p, ssp, e)
             => new object[]
             {
                    JoinType.Inner, p.ID == ssp.ProductionOrderId ,
                    JoinType.Inner, ssp.EquipmentId == e.ID
             })
             .Where((p, ssp, e) => p.ID == model.ProductionOrderId)
             .Select((p, ssp, e) => new PoSegmentParameterEntity
             {
                 ID = ssp.ID,
                 ProductionOrderId= p.ID,
                 SapSegmentParameterId = ssp.SapSegmentParameterId,
                 ParameterName = ssp.ParameterName,
                 ParameterValue = ssp.ParameterValue,
                 DataBlock = ssp.DataBlock,
                 DataBlockItem = ssp.DataBlockItem,
                 EquipmentId = ssp.EquipmentId,
                 EquipmentCode = e.EquipmentCode,
                 CreateDate = p.CreateDate,
                 CreateUserId = p.CreateUserId,
                 ModifyDate = p.ModifyDate,
                 ModifyUserId = p.ModifyUserId
             })
             .MergeTable()
             .OrderBy(a => a.EquipmentCode).ToPageListAsync(model.pageIndex,model.pageSize, count);

            data.data = list;
            data.dataCount = count;
            return data;

        }


        public async Task<PageModel<PoSegmentParameterEntity>> GetPageList(PoSegmentParameterRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PoSegmentParameterEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(PoSegmentParameterEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}
using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    public class BatchPalletSelectmViewRequestModel : RequestPageModelBase
    {
        public BatchPalletSelectmViewRequestModel()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal IQuantity { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ContainerId { get; set; }

    }
}
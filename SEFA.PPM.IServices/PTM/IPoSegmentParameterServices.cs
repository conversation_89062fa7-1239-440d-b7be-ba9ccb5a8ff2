using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPoSegmentParameterServices
	/// </summary>	
    public interface IPoSegmentParameterServices :IBaseServices<PoSegmentParameterEntity>
	{
		Task<PageModel<PoSegmentParameterEntity>> GetPageList(PoSegmentParameterRequestModel reqModel);

        Task<List<PoSegmentParameterEntity>> GetList(PoSegmentParameterRequestModel reqModel);

		Task<bool> SaveForm(PoSegmentParameterEntity entity);

        Task<PageModel<PoSegmentParameterEntity>> GetPoSegmentParameterList(PoSegmentParameterRequestModel model);
    }
}
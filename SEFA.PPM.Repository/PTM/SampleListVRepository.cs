using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// SampleListVRepository
    /// </summary>
    public class SampleListVRepository : BaseRepository<SampleListVEntity>, ISampleListVRepository
    {
        public SampleListVRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}
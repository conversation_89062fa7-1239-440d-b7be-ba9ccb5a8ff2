using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackRecipeViewRepository
	/// </summary>
    public class PackRecipeViewRepository : BaseRepository<PackRecipeViewEntity>, IPackRecipeViewRepository
    {
        public PackRecipeViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}
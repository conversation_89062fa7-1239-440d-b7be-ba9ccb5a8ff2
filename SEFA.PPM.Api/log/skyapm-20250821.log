2025-08-21 11:16:30.038 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Initializing ...
2025-08-21 11:16:30.067 +08:00 [FA_DFM] [Information] SkyApm.Service.RegisterService : Loaded instrument service [SkyApm.Service.RegisterService].
2025-08-21 11:16:30.070 +08:00 [FA_DFM] [Information] SkyApm.Service.PingService : Loaded instrument service [SkyApm.Service.PingService].
2025-08-21 11:16:30.070 +08:00 [FA_DFM] [Information] SkyApm.Service.SegmentReportService : Loaded instrument service [SkyApm.Service.SegmentReportService].
2025-08-21 11:16:30.070 +08:00 [FA_DFM] [Information] SkyApm.Service.CLRStatsService : Loaded instrument service [SkyApm.Service.CLRStatsService].
2025-08-21 11:16:30.071 +08:00 [FA_DFM] [Information] SkyApm.Sampling.SimpleCountSamplingInterceptor : Loaded instrument service [SkyApm.Sampling.SimpleCountSamplingInterceptor].
2025-08-21 11:16:30.073 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectService : Loaded instrument service [SkyApm.Transport.Grpc.ConnectService].
2025-08-21 11:16:30.160 +08:00 [FA_DFM] [Information] SkyApm.Diagnostics.TracingDiagnosticProcessorObserver : Loaded diagnostic listener [Microsoft.AspNetCore].
2025-08-21 11:16:30.160 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Started SkyAPM .NET Core Agent.
2025-08-21 11:16:40.560 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:16:45.144 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:16:55.278 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:17:00.164 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:17:10.251 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:17:15.109 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:17:25.176 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:17:30.112 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:17:40.153 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:17:45.096 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:17:55.124 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:18:00.127 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:18:10.173 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:18:15.089 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:18:25.118 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:18:30.098 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:18:40.133 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:18:45.086 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:18:55.117 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:19:00.122 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:19:10.154 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:19:15.094 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:19:25.129 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:19:30.113 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:19:40.143 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:19:45.094 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:19:55.125 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:20:00.101 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:20:10.133 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-21 11:20:15.089 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-21 11:36:56.416 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Initializing ...
2025-08-21 11:36:56.445 +08:00 [FA_DFM] [Information] SkyApm.Service.RegisterService : Loaded instrument service [SkyApm.Service.RegisterService].
2025-08-21 11:36:56.450 +08:00 [FA_DFM] [Information] SkyApm.Service.PingService : Loaded instrument service [SkyApm.Service.PingService].
2025-08-21 11:36:56.450 +08:00 [FA_DFM] [Information] SkyApm.Service.SegmentReportService : Loaded instrument service [SkyApm.Service.SegmentReportService].
2025-08-21 11:36:56.451 +08:00 [FA_DFM] [Information] SkyApm.Service.CLRStatsService : Loaded instrument service [SkyApm.Service.CLRStatsService].
2025-08-21 11:36:56.451 +08:00 [FA_DFM] [Information] SkyApm.Sampling.SimpleCountSamplingInterceptor : Loaded instrument service [SkyApm.Sampling.SimpleCountSamplingInterceptor].
2025-08-21 11:36:56.451 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectService : Loaded instrument service [SkyApm.Transport.Grpc.ConnectService].
2025-08-21 11:36:56.527 +08:00 [FA_DFM] [Information] SkyApm.Diagnostics.TracingDiagnosticProcessorObserver : Loaded diagnostic listener [Microsoft.AspNetCore].
2025-08-21 11:36:56.527 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Started SkyAPM .NET Core Agent.

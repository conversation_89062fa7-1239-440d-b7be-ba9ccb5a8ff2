using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ILosstgtViewServices
	/// </summary>	
    public interface ILosstgtViewServices :IBaseServices<LosstgtViewEntity>
	{
		Task<PageModel<LosstgtViewEntity>> GetPageList(LosstgtViewRequestModel reqModel);

        Task<List<LosstgtViewEntity>> GetList(LosstgtViewRequestModel reqModel);

		Task<bool> SaveForm(LosstgtViewEntity entity);
    }
}
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsChargeRequestServices
	/// </summary>	
    public interface IDcsChargeRequestServices :IBaseServices<DcsChargeRequestEntity>
	{
		Task<PageModel<DcsChargeRequestEntity>> GetPageList(DcsChargeRequestModel reqModel);

        Task<List<DcsChargeRequestEntity>> GetList(DcsChargeRequestModel reqModel);

		Task<bool> SaveForm(DcsChargeRequestEntity entity);
    }
}
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PTM;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{
	/// <summary>
	/// IAndonServices
	/// </summary>	
	public interface IDcsServices
    {
		Task<List<DcsMsgModel>> GetPoDcsInfo(BatchStateModel model);

		Task<MessageModel<DcsChargeRequestEntity>> ScanInputMaterialEquipmentCode(DcsChargeRequestModel model);

    }
}
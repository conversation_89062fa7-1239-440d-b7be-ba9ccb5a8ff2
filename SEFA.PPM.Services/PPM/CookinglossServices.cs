
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.Helper;
using Magicodes.ExporterAndImporter.Excel;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.Services
{
    public class CookinglossServices : BaseServices<CookinglossEntity>, ICookinglossServices
    {
        private readonly IBaseRepository<CookinglossEntity> _dal;
        public IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        public CookinglossServices(IBaseRepository<CookinglossEntity> dal, IUser user ,IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            _user = user;
            _unitOfWork = unitOfWork;
            base.BaseDal = dal;
        }

        public async Task<List<CookinglossEntity>> GetList(CookinglossRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CookinglossEntity, MKM.Model.Models.MaterialEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineId), (p, m) => p.LineId == reqModel.LineId)
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatId), (p, m) => p.MatId == reqModel.MatId)
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineName), (p, m) => p.LineName.Contains(reqModel.LineName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatName), (p, m) => p.MatName.Contains(reqModel.MatName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.name), (p, m) => p.LineName.Contains(reqModel.name) || p.MatName.Contains(reqModel.name)
                                              || m.Description.Contains(reqModel.name) || m.Code.Contains(reqModel.name))
                             .ToExpression();
            var data = await _dal.QueryTwoTables<CookinglossEntity, MKM.Model.Models.MaterialEntity, CookinglossEntity>
                              (
                                    (p, m) => new object[]
                                    {
                                        JoinType.Inner, p.MatId == m.ID
                                    },
                                    (p, m) => new CookinglossEntity
                                    {
                                        ID = p.ID,
                                        LineId = p.LineId,
                                        LineName = p.LineName,
                                        MatId = p.MatId,
                                        MatName = p.MatName,
                                        PertankLose = p.PertankLose,
                                        PipeLose = p.PipeLose,
                                        ReservoirLose = p.ReservoirLose,
                                        TankCapacity = p.TankCapacity,
                                        TankwallLose = p.TankwallLose,
                                        Deleted = p.Deleted,
                                        UpdateTimeStamp = p.UpdateTimeStamp,
                                        CreateDate = p.CreateDate,
                                        CreateUserId = p.CreateUserId,
                                        ModifyDate = p.ModifyDate,
                                        ModifyUserId = p.ModifyUserId,
                                        MatCode = m.Description,
                                        MtrCode = m.Code                                    
                                    },
                                    whereExpression
                              );
            //var whereExpression = Expressionable.Create<CookinglossEntity>()
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.LineId), p => p.LineId == reqModel.LineId)
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.MatId), p => p.MatId == reqModel.MatId)
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.LineName), p => p.LineName.Contains(reqModel.LineName))
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.MatName), p => p.MatName.Contains(reqModel.MatName))
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.name), p => p.LineName.Contains(reqModel.name) || p.MatName.Contains(reqModel.name))
            //                 .ToExpression();
            //var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<CookinglossEntity>> GetPageList(CookinglossRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CookinglossEntity, MKM.Model.Models.MaterialEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineId), (p, m) => p.LineId == reqModel.LineId)
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatId), (p, m) => p.MatId == reqModel.MatId)
                             .AndIF(!string.IsNullOrEmpty(reqModel.LineName), (p, m) => p.LineName.Contains(reqModel.LineName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatName), (p, m) => p.MatName.Contains(reqModel.MatName))
                             .AndIF(!string.IsNullOrEmpty(reqModel.name), (p, m) => p.LineName.Contains(reqModel.name) || p.MatName.Contains(reqModel.name)
                                              || m.Description.Contains(reqModel.name) || m.Code.Contains(reqModel.name))
                             .ToExpression();
            var data = await _dal.QueryTabsPage<CookinglossEntity, MKM.Model.Models.MaterialEntity, CookinglossEntity>
                              (
                                    (p, m) => new object[]
                                    {
                                        JoinType.Inner, p.MatId == m.ID
                                    },
                                    (p, m) => new CookinglossEntity
                                    {
                                        ID = p.ID,
                                        LineId = p.LineId,
                                        LineName = p.LineName,
                                        MatId = p.MatId,
                                        MatName = p.MatName,
                                        PertankLose = p.PertankLose,
                                        PipeLose = p.PipeLose,
                                        ReservoirLose = p.ReservoirLose,
                                        TankCapacity = p.TankCapacity,
                                        TankwallLose = p.TankwallLose,
                                        Deleted = p.Deleted,
                                        UpdateTimeStamp = p.UpdateTimeStamp,
                                        CreateDate = p.CreateDate,
                                        CreateUserId = p.CreateUserId,
                                        ModifyDate = p.ModifyDate,
                                        ModifyUserId = p.ModifyUserId,
                                        MatCode = m.Description,
                                        MtrCode = m.Code
                                    },
                                    whereExpression,
                                    reqModel.pageIndex,
                                    reqModel.pageSize
                              );
            return data;
        }

        public async Task<bool> SaveForm(CookinglossEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.Create(_user.Name.ToString());
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="input">文件流</param>
        /// <returns></returns>
        public async Task<ResultString> ImportData(FileImportDto input)
        {
            ResultString result = new ResultString();
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<CookinglossExcelDto>(stream);
            if (import.Data.Count() < 1)
            {
                result.AddError("表格中没有效数据");
                return result;
            }
            // 返回 导入异常信息
            if (import.Exception != null)
            {
                result.AddError(import.Exception);
                return result;
            }

            var excelData = import.Data.Where(x => !string.IsNullOrEmpty(x.LineName) && !string.IsNullOrEmpty(x.MtrCode) && x.TankCapacity >0 && x.PertankLose >=0 && x.PipeLose >=0 && x.TankwallLose >=0 && x.ReservoirLose >=0).ToList();
            if (excelData.Count() < 1)
            {
                result.AddError("表格中无有效数据(1、产线，配方物料代码不能为空;2、损耗数大于等于0 ;3、缸重大于0)");
                return result;
            }

            var mtrInfos = await  _dal.Db.Queryable<MaterialEntity>().Where(p => !string.IsNullOrEmpty(p.Description)).ToListAsync();
            var lineInfos = await _dal.Db.Queryable<EquipmentEntity>().Where(p => p.Level == "line").ToListAsync();

            var allData = await this.FindList(m => m.Deleted == 0);

            var addList = new List<CookinglossEntity>();
            for (int i = 0; i < excelData.Count; i++)
            {
                var item = excelData[i];
                if (string.IsNullOrEmpty(item.LineName) || string.IsNullOrEmpty(item.MtrCode) || item.TankCapacity < 0 ||item.PertankLose < 0 || item.PipeLose < 0 || item.TankwallLose < 0 && item.ReservoirLose < 0)
                    continue;

                var mtrEnity = mtrInfos.Where(x => x.Code == item.MtrCode).FirstOrDefault();
                if (mtrEnity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到配方物料[{item.MtrCode}]基础表信息数据");
                    return result;
                }
                var line = lineInfos.Where(x => x.EquipmentCode == item.LineName || x.EquipmentName == item.LineName).FirstOrDefault();
                if (line == null)
                {
                    result.AddError($"第[{i + 1}]行未找到产线代码[{item.LineName}]的基础表信息数据");
                    return result;
                }

                var entity = allData.Where(p => p.MatId == mtrEnity.ID && p.LineId == line.ID && p.TankCapacity == item.TankCapacity ).FirstOrDefault();
                if (!addList.Any(p => p.MatId == mtrEnity.ID && p.LineId == line.ID && p.TankCapacity == item.TankCapacity))
                {
                    entity = new CookinglossEntity();
                    entity.LineId = line.LineId;
                    entity.LineName = line.EquipmentCode;
                    entity.MatId = mtrEnity.ID;
                    entity.MatName = mtrEnity.NAME;
                    entity.TankCapacity = item.TankCapacity;
                    entity.TankwallLose = item.TankwallLose;
                    entity.PipeLose = item.PipeLose;
                    entity.ReservoirLose = item.ReservoirLose;
                    entity.PertankLose = item.PertankLose;
                    entity.CreateCustomGuid(_user.Name);
                    addList.Add(entity);
                }
            }
            _unitOfWork.BeginTran();
            try
            {
                if (allData.Any() && addList.Any())
                {
                    var ids = allData.Select(a => a.ID).ToArray();
                    await this.DeleteByIds(ids);
                }

                if (addList.Any())
                {
                    await this.Add(addList);
                }

                _unitOfWork.CommitTran();

                result.Data += $"删除数据{allData.Count()}条,新增数据{addList.Count()}条";
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.AddError(ex.Message);
            }

            return result;

        }
    }
}
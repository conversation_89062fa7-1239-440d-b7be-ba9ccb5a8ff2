using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDcsMaterialTransferServices
	/// </summary>	
    public interface IDcsMaterialTransferServices :IBaseServices<DcsMaterialTransferEntity>
	{
		Task<PageModel<DcsMaterialTransferEntity>> GetPageList(DcsMaterialTransferRequestModel reqModel);

        Task<List<DcsMaterialTransferEntity>> GetList(DcsMaterialTransferRequestModel reqModel);

		Task<bool> SaveForm(DcsMaterialTransferEntity entity);
    }
}
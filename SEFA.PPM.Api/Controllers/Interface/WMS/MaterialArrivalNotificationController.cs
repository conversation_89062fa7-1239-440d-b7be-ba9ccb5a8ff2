using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.PPM.IServices;

namespace SEFA.PPM.Controllers.Interface.WMS
{
    [ApiController]
    [Route("api/interface/wms/[controller]")]
    public class MaterialArrivalNotificationController : ControllerBase
    {
        private readonly IWmsServices _wmsServices;
        private readonly ILogger<MaterialArrivalNotificationController> _logger;

        public MaterialArrivalNotificationController(IWmsServices wmsServices, ILogger<MaterialArrivalNotificationController> logger)
        {
            _wmsServices = wmsServices;
            _logger = logger;
        }

        [HttpPost("SendDistributionMaterial")]
        public MessageModel<string> SendDistributionMaterial([FromBody] MaterialArrivalNotificationRequest request)
        {
            try
            {
                _logger.LogInformation("Received material arrival notification: {RequestSheetNo}", request.RequestSheetNo);
                
                // Call service layer
                // var result = _wmsServices.SendDistributionMaterialAsync(request);
                
                _logger.LogInformation("Material arrival notification processed successfully: {RequestSheetNo}", request.RequestSheetNo);
                
                return MessageModel<string>.Success("Material arrival notification processed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing material arrival notification: {RequestSheetNo}", request.RequestSheetNo);
                return MessageModel<string>.Fail(ex.Message);
            }
        }
    }

    public class MaterialArrivalNotificationRequest
    {
        public string RequestSheetNo { get; set; }
        public string LineWarehouseCode { get; set; }
        public List<MaterialArrivalDetail> DetailsList { get; set; }
    }

    public class MaterialArrivalDetail
    {
        public string Plant { get; set; }
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }
        public string MaterialVersionCode { get; set; }
        public string BatchNo { get; set; }
        public string PalletNo { get; set; }
        public string BarCode { get; set; }
        public double Quantity { get; set; }
        public string Unit { get; set; }
        public double Density { get; set; }
        public double CoAContent { get; set; }
        public string Remark { get; set; }
    }
}
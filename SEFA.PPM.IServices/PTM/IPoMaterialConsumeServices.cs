using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPoMaterialConsumeServices
	/// </summary>	
    public interface IPoMaterialConsumeServices :IBaseServices<PoMaterialConsumeEntity>
	{
		Task<PageModel<PoMaterialConsumeEntity>> GetPageList(PoMaterialConsumeRequestModel reqModel);

        Task<List<PoMaterialConsumeEntity>> GetList(PoMaterialConsumeRequestModel reqModel);

		Task<bool> SaveForm(PoMaterialConsumeEntity entity);
    }
}
namespace SEFA.PPM.Model.Models.Interface.WMS;

/// <summary>
/// 线边物料出库请求明细
/// </summary>
public class LineWarehouseMaterialOutBoundDetail
{
    /// <summary>
    /// 物料编码
    /// </summary>
    public string MaterialCode { get; set; }

    /// <summary>
    /// 物料描述
    /// </summary>
    public string MaterialName { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string Supplier { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public double Quantity { get; set; }

    /// <summary>
    /// 计量单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 备注说明
    /// </summary>
    public string Remark { get; set; }
}
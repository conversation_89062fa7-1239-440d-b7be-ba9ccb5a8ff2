
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using AutoMapper;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Extensions;
using SEFA.Base.Common.LogHelper;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using System;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using static SEFA.PTM.Services.ConsumeViewServices;
using System.Linq;

namespace SEFA.PPM.Services
{
    public class SappackorderrecordServices : BaseServices<SappackorderrecordEntity>, ISappackorderrecordServices
    {
        private readonly IBaseRepository<SappackorderrecordEntity> _dal;
        private readonly IBaseRepository<SappackorderEntity> _sapPackOrderdal;
        private readonly IBaseRepository<DFM.Model.Models.MaterialEntity> _materialDal;
        private readonly IBaseRepository<DFM.Model.Models.MaterialVersionEntity> _materiaVersionlDal;
        private readonly IBaseRepository<MKM.Model.Models.EquipmentEntity> _equipmentDal;
        private readonly IBaseRepository<ProductionOrderEntity> _proOrderdal;
        private readonly IOrderBomServices _bomService;
        private readonly ISappackorderServices _sapPackService;
        private readonly IBProductionOrderListViewServices _bpListViewService;
        private readonly IBaseRepository<OrderrealtionEntity> _orderrelationDal;
        private readonly IBaseRepository<FormulascheduleEntity> _formulaScheduleDal;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRedisBasketRepository _redisBasketRepository;
        private readonly IAndonServices _andonServices;

        private readonly string logName = "处理SapPackOrdr";
        public SappackorderrecordServices(
            IBaseRepository<SappackorderrecordEntity> dal,
            IBaseRepository<SappackorderEntity> sapPackOrderdal,
            IMapper mapper,
            IBaseRepository<DFM.Model.Models.MaterialEntity> materialDal,
            IBaseRepository<DFM.Model.Models.MaterialVersionEntity> materiaVersionlDal,
            IBaseRepository<MKM.Model.Models.EquipmentEntity> equipmentDal,
            IBaseRepository<ProductionOrderEntity> proOrderdal,
            IBProductionOrderListViewServices bProductionOrderListViewServices,
            IOrderBomServices bomService,
            ISappackorderServices sapPackService,
            IUnitOfWork unitOfWork,
            IBaseRepository<OrderrealtionEntity> orderrelationDal,
            IBaseRepository<FormulascheduleEntity> formulaScheduleDal,
            IRedisBasketRepository redisBasketRepository,
            IAndonServices andonServices)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _sapPackOrderdal = sapPackOrderdal;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            this._materialDal = materialDal;
            this._materiaVersionlDal = materiaVersionlDal;
            this._equipmentDal = equipmentDal;
            this._proOrderdal = proOrderdal;
            this._sapPackService = sapPackService;
            this._bomService = bomService;
            this._bpListViewService = bProductionOrderListViewServices;
            this._orderrelationDal = orderrelationDal;
            this._formulaScheduleDal = formulaScheduleDal;
            this._redisBasketRepository = redisBasketRepository;
            this._andonServices = andonServices;
        }

        public async Task<List<SappackorderrecordEntity>> GetList(SappackorderrecordRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SappackorderrecordEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<SappackorderrecordEntity>> GetPageList(SappackorderrecordRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SappackorderrecordEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(SappackorderrecordEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 处理SAPPackOrder
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> DealSapPackOrder() {
            var result = new MessageModel<string>();
            result.success = true;
            string key = "PPM_DealSapPackOrder";

            if(await _redisBasketRepository.Exist(key))
            {
                SerilogServer.LogDebug("本次存在正在执行的任务,退出", logName);
                return result;
            }
            try
            {
                await _redisBasketRepository.Set(key, "1", new TimeSpan(0, 0, 10, 0));
                result = await ExecuteData();
            }
            catch(Exception ex )
            {
                result.msg = "处理SapPackOrder异常：" + ex.ToString();
                SerilogServer.LogDebug(result.msg, logName);
            }
            finally
            {
                await _redisBasketRepository.Remove(key);
            }
            return result;
        }

        public async Task<MessageModel<string>> ExecuteData () {
            var result = new MessageModel<string>();
            var waitlist = await _dal.FindList(a => a.RecordFlag == 0, a => a.CreateDate);
            if(waitlist.Count == 0)
            {
                SerilogServer.LogDebug("本次无待处理的工单,退出", logName);
                return result;
            }
            DateTime start = DateTime.Now;
            try
            {
                /*工单类型
                  *zxh1灌包工单 
                  *zxh9灌包装的返工工单 
                  *zxh2煮制工单 
                  *zxh4煮制返工工单 
                  *暂时排除 zxh2
              */
                var orderTypeList = new List<string>() { "ZXH1", "ZXH4", "ZXH9" };
                var newMvList = new List<MaterialVersionEntity>();
                var equipmentList = await _equipmentDal.FindList(a => a.Deleted == 0);
                var mcodeList = waitlist.Select(a => a.Matnr).Distinct();
                foreach(var item in waitlist.Select(a => a.MatnrComp).Distinct())
                {
                    if (mcodeList.Contains(item))
                        continue;
                    mcodeList.Append(item);
                }
                var mList = await _materialDal.FindList(a => mcodeList.Contains(a.Code) && a.Deleted == 0);
                var mIdList = mList.Select(a => a.ID).Distinct();
                var mvList = await _materiaVersionlDal.FindList(a => mIdList.Contains(a.MaterialId));

                var plandateList = waitlist.Select(a => a.Gstrp).Distinct();
                var formulaScheduleList = await _formulaScheduleDal.FindList(a => plandateList.Contains(a.ProduceDate));


                var aufnrList = waitlist.Select(a => a.Aufnr).Distinct();
                var existOrderList = await _proOrderdal.FindList(p => aufnrList.Contains(p.ProductionOrderNo));

                _unitOfWork.BeginTran();
                try
                {
                    SerilogServer.LogDebug($"本次接口工单:{waitlist.Count},处理工单信息开始", logName);

                    List<SappackorderEntity> addList = new List<SappackorderEntity>();
                    List<SappackorderEntity> updateList = new List<SappackorderEntity>();

                    List<ProductionOrderEntity> addProdList = new List<ProductionOrderEntity>();
                    List<ProductionOrderEntity> updateProdList = new List<ProductionOrderEntity>();
                    List<ProductionOrderEntity> deleteProdList = new List<ProductionOrderEntity>();

                    List<OrderrealtionEntity> orderrealtionEntities = new List<OrderrealtionEntity>();

                    List<string> needRePublicBatchPoList = new List<string>();


                    foreach (var data in waitlist)
                    {
                        data.Modify(data.ID, "Job");
                        var curOrderList = existOrderList.Where(a => a.ProductionOrderNo == data.Aufnr).OrderByDescending(a => a.CreateDate);

                        bool changed = false;

                        #region 基础信息检查
                        if (orderTypeList.Contains(data.Auart, StringComparer.OrdinalIgnoreCase) == false)
                        {
                            data.RecordFlag = -1;
                            data.ErrorMsg = $"工单号[{data.Aufnr}]类型[{data.Auart}]无效";
                            SerilogServer.LogDebug(data.ErrorMsg, logName);
                            continue;
                        }

                        /*获取产线*/
                        var segmentEntity = equipmentList.Where(x => x.EquipmentCode == data.Arbpl).FirstOrDefault();
                        if (segmentEntity == null)
                        {
                            data.RecordFlag = -1;
                            data.ErrorMsg = $"工单号[{data.Aufnr}]工作中心[{data.Arbpl}]未维护";
                            SerilogServer.LogDebug(data.ErrorMsg, logName);
                            continue;
                        }
                        var lineEntity = equipmentList.Where(x => x.ID == segmentEntity.ParentId).FirstOrDefault();

                        var material = mList.Where(m => m.Code == data.Matnr).FirstOrDefault();

                        if (material == null)
                        {   
                            data.RecordFlag = -1;  
                            data.ErrorMsg = $"工单号[{data.Aufnr}]物料编码[{data.Matnr}]未维护";
                            SerilogServer.LogDebug(data.ErrorMsg, logName);
                            continue;
                        }

                        #endregion 基础信息检查

                        SappackorderEntity obj = await _sapPackOrderdal.FindEntity(p => p.Aufnr == data.Aufnr);
                        if (obj == null)
                        {
                          
                            obj = _mapper.Map<SappackorderEntity>(data);
                            obj.Opstatus = "A";
                            obj.PPstatus = "A";
                            obj.ChangeRecord = $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]接收SAP工单\n";
                            obj.CreateCustomGuid("SAP_PKGORDGET");

                            if (data.Status != null && data.Status.Contains("DLFL"))
                            {
                                obj.Opstatus = "D";
                                obj.PPstatus = "D";
                                obj.ChangeRecord += $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]SAP删除工单\n";
                            }

                            addList.Add(obj);

                            if (obj.Arbpl.StartsWith("FIL") && !String.IsNullOrEmpty(obj.MatnrComp) && !obj.Status.Contains("DLFL"))
                            {
                                /*暂时不考虑特殊配方，后期需修改 20241204*/
                                var formulaSchedule = formulaScheduleList.Where(a => a.FillLineID == lineEntity.ID && a.ProduceDate == data.Gstrp && a.SapFormula == data.Normt).FirstOrDefault();
                                if (formulaSchedule != null)
                                {
                                    OrderrealtionEntity orderrealtionEntity = new OrderrealtionEntity();
                                    orderrealtionEntity.CookorderId = formulaSchedule.ID;
                                    orderrealtionEntity.CookorderCode = "F";
                                    orderrealtionEntity.FillorderId = obj.ID;
                                    orderrealtionEntity.FillorderCode = obj.Aufnr;
                                    orderrealtionEntity.CreateCustomGuid("admin");

                                    orderrealtionEntities.Add(orderrealtionEntity);

                                    SerilogServer.LogDebug($"工单号[{data.Aufnr}][{data.Gstrp}][{data.Normt}]与配方关联", logName);
                                }
                            }
                        }
                        else
                        {
                            string remark = string.Empty;
                            
                            if (obj.Matnr != data.Matnr)
                            {
                                changed = true;
                                remark += $"订单料号:由[{obj.Matnr}]变更为[{data.Matnr}]\n";
                            }
                            if (obj.Psmng != data.Psmng)
                            {
                                changed = true;
                                remark += $"计划数量:由[{obj.Psmng}]变更为[{data.Psmng}]\n";
                            }
                            if (obj.Arbpl != data.Arbpl)
                            {
                                changed = true;
                                remark += $"工作中心:由[{obj.Arbpl}]变更为[{data.Arbpl}]\n";
                            }
                            if (obj.Magrv != data.Magrv)
                            {
                                changed = true;
                                remark += $"销售容器由[{obj.Magrv}]变更为[{data.Magrv}]\n";
                            }
                            if (obj.Gstrp != data.Gstrp)
                            {
                                changed = true;
                                remark += $"计划日期:由[{obj.Gstrp}]变更为[{data.Gstrp}]\n";
                            }
                            if (obj.OrderJarClear != data.OrderJarClear)
                            {
                                changed = true;
                                remark += $"PMC备注:由[{obj.OrderJarClear}]变更为[{data.OrderJarClear}]\n";
                            }
                            if (obj.Zcylcd != data.Zcylcd)
                            {
                                changed = true;
                                remark += $"清缸代码:由[{obj.Zcylcd}]变更为[{data.Zcylcd}]\n";
                            }
                            if (obj.Codtx != data.Codtx)
                            {
                                changed = true;
                                remark += $"清缸描述:由[{obj.Codtx}]变更为[{data.Codtx}]\n";
                            }
                            if (obj.Dispo != data.Dispo)
                            {
                                changed = true;
                                remark += $"MRP:由[{obj.Dispo}]变更为[{data.Dispo}]\n";
                            }
                            if (obj.Normt != data.Normt)
                            {
                                changed = true;
                                remark += $"生产配方:由[{obj.Normt}]变更为[{data.Normt}]\n";
                            }
                            if (obj.MatnrComp != data.MatnrComp)
                            {
                                changed = true;
                                remark += $"生产配方物料:由[{obj.MatnrComp}]变更为[{data.MatnrComp}]\n";
                            }
                          
                            if (changed)
                            {
                                obj.ChangeRecord += $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]\n" + remark;
                                obj.Opstatus = "U";
                                obj.PPstatus = "U";
                                if (obj.Gstrp >= DateTime.Now.Date && DateTime.Now.Date.AddDays(2) >= obj.Gstrp)
                                {
                                    /*发送Andon*/
                                    try
                                    {
                                        await _andonServices.PackOrderChange(obj.Aufnr, lineEntity.EquipmentCode, obj.Gstrp.Value, remark);
                                    }
                                    catch (Exception e) { }
                                }
                            }

                            if (data.Status != null && data.Status.Contains("DLFL") && !obj.Status.Contains("DLFL"))
                            {
                                obj.Opstatus = "D";
                                obj.PPstatus = "D";
                                obj.ChangeRecord += $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]SAP删除工单\n";
                                if (obj.Gstrp >= DateTime.Now.Date && DateTime.Now.Date.AddDays(2) >= obj.Gstrp)
                                {
                                    /*发送Andon*/
                                    try
                                    {
                                        await _andonServices.PackOrderChange(obj.Aufnr, lineEntity.EquipmentCode, obj.Gstrp.Value, $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]SAP删除工单\n");
                                    }
                                    catch (Exception e) { }
                                }
                            }

                            obj = _mapper.Map<SappackorderEntity>(data);
                            obj.Modify(obj.ID, "SAP_PKGORDGET");
                            updateList.Add(obj);
                        }

                        var materialVersion = mvList.Where(m => m.MaterialId == material.ID).FirstOrDefault();
                        if (materialVersion == null)
                        {
                            materialVersion = newMvList.Where(a => a.MaterialId == material.ID).FirstOrDefault();
                            if (materialVersion == null)
                            {
                                materialVersion = new MaterialVersionEntity();
                                materialVersion.MaterialId = material.ID;
                                materialVersion.MaterialVersionNumber = "0001";
                                materialVersion.CreateCustomGuid("admin");
                                newMvList.Add(materialVersion);
                            }
                        }
                        if (curOrderList.Count() == 0)
                        {
                            /*配方料号Id*/
                            var FormulaMaterialId = string.Empty;
                            if (string.IsNullOrWhiteSpace(data.AuartFill))
                            {
                                FormulaMaterialId = mList.Where(a => a.Code == data.MatnrComp).FirstOrDefault()?.ID;
                                if (string.IsNullOrEmpty(FormulaMaterialId))
                                {
                                    SerilogServer.LogDebug($"ProductionOrder工单号[{data.Aufnr}]无AUART_FILL,组件料号[{data.MatnrComp}]FormulaMaterialId为空", "接收SAP工单 ");
                                }
                            }
                            else
                            {
                                var fillOrder = waitlist.FirstOrDefault(a => a.Aufnr == data.AuartFill);
                                if (fillOrder != null)
                                {
                                    FormulaMaterialId = mList.Where(a => a.Code == fillOrder.MatnrComp).FirstOrDefault()?.ID;
                                    if (string.IsNullOrEmpty(FormulaMaterialId))
                                    {
                                        SerilogServer.LogDebug($"ProductionOrder工单号[{data.Aufnr}]对应灌装工单[{fillOrder.Aufnr}]存在AUART_FILL[{data.AuartFill}]组件料号[{fillOrder.MatnrComp}] FormulaMaterialId为空", "接收SAP工单 ");
                                    }
                                }
                                else
                                {
                                    var fillPoEntity = await _proOrderdal.FindList(a => a.ProductionOrderNo == data.AuartFill);
                                    if (fillPoEntity.Count > 0)
                                    {
                                        FormulaMaterialId = fillPoEntity.First().FormulaMaterialId;
                                        SerilogServer.LogDebug($"ProductionOrder工单号[{data.Aufnr}]存在AUART_FILL[{data.AuartFill}],存在PO表灌装工单[{data.AuartFill}]FormulaMaterialId=[{FormulaMaterialId}]", "接收SAP工单 ");
                                    }
                                    else
                                    {
                                        if (string.IsNullOrEmpty(FormulaMaterialId))
                                        {
                                            SerilogServer.LogDebug($"ProductionOrder工单号[{data.Aufnr}]存在AUART_FILL[{data.AuartFill}],不存在PO表灌装工单[{data.AuartFill}]", "接收SAP工单 ");
                                        }
                                    }
                                }
                            }


                            ProductionOrderEntity prod = new ProductionOrderEntity()
                            {
                                FormulaMaterialId = FormulaMaterialId,
                                PlanDate = data.Gstrp,
                                FillLineId = lineEntity.ID,
                                LineCode = lineEntity.EquipmentCode,
                                SegmentCode = data.Arbpl,
                                ProductionOrderNo = data.Aufnr,
                                PlanQty = data.Psmng,
                                Type = "WorkOrder",
                                MaterialVersionId = materialVersion.ID,
                                MaterialId = material.ID,
                                PlanStartTime = data.Gstrp,
                                SapOrderType = data.Auart,
                                PDType = data.Pdtype,
                                SapFormula = data.Normt,
                                //  MesOrderCode = "P" + (co + i),
                                PoStatus = "1",
                                QaStatus = data.Arbpl.Contains("OVR-PRNT") ? "通过" : "待QA",
                                OrderType = "C"
                            };
                            prod.CreateCustomGuid("SAP_PKGORDGET");

                            addProdList.Add(prod);
                        }
                        else
                        {
                            var existOrder = curOrderList.Where(a => a.Type == "WorkOrder").OrderByDescending(p => p.CreateDate).FirstOrDefault();
                            if (obj.Opstatus == "D")
                            {
                                /*工单已删除则废弃工单*/
                                existOrder.PoStatus = "4";
                                if (needRePublicBatchPoList.Contains(obj.Aufnr))
                                {
                                    needRePublicBatchPoList.Remove(obj.Aufnr);
                                }

                                SerilogServer.LogDebug($"工单号[{data.Aufnr}]被删除,更新工单状态为 4 已取消", logName);
                            }
                            else
                            {
                                if (changed)
                                {
                                    SerilogServer.LogDebug($"ProductionOrder工单号[{data.Aufnr}]原数量为[{existOrder.PlanQty}],原计划日期[{existOrder.PlanStartTime}],原工作中心[{existOrder.SegmentCode}]", logName);
                                    existOrder.PlanDate = data.Gstrp;
                                    existOrder.PlanQty = data.Psmng;
                                    existOrder.PlanStartTime = data.Gstrp;
                                    existOrder.SegmentCode = data.Arbpl;
                                    existOrder.LineCode = lineEntity.EquipmentCode;
                                    existOrder.FillLineId = lineEntity.ID;
                                    //existOrder.FormulaMaterialId = FormulaMaterialId;
                                    updateProdList.Add(existOrder);

                                    if (existOrder.PoStatus == "2")
                                    {
                                        needRePublicBatchPoList.Add(obj.Aufnr);
                                    }

                                    SerilogServer.LogDebug($"ProductionOrder工单号[{data.Aufnr}]修改数量为[{existOrder.PlanQty}],修改计划日期[{existOrder.PlanStartTime}],修改工作中心[{existOrder.SegmentCode}]", logName);
                                }
                            }
                            


                            var deleteList = curOrderList.Where(p => p.ID != existOrder.ID);
                            if (deleteList.Count() > 0)
                            {
                                foreach (var item in deleteList)
                                {
                                    if (deleteProdList.Contains(item))
                                        continue;
                                    deleteProdList.Add(item);
                                }

                                SerilogServer.LogDebug($"删除工单[{string.Join(',', deleteList.Select(a=>a.MesOrderCode))}]", logName);
                            }
                        }

                        data.RecordFlag = 100;

                    }

                    
                    TimeSpan span1 = DateTime.Now - start;
                    SerilogServer.LogDebug($"处理工单耗时:{span1.TotalSeconds}", logName);

                    var RePublicBatchPoList = existOrderList.Where(a => needRePublicBatchPoList.Contains(a.ProductionOrderNo)).ToList();
                    if (RePublicBatchPoList.Count > 0)
                    {
                        SerilogServer.LogDebug($"需要RebuildBatch工单数量[{RePublicBatchPoList.Count}][{string.Join(",", RePublicBatchPoList.Select(a => a.ProductionOrderNo))}]", logName);

                        DateTime btime = DateTime.Now;
                        await _bpListViewService.RebuildBatch2(RePublicBatchPoList.Select(a => a.ID).ToList());
                        TimeSpan span = DateTime.Now - btime;
                        SerilogServer.LogDebug($"RebuildBatch2共耗时:{span.TotalSeconds}", logName);
                    }
                    if (waitlist.Any())
                    {
                        await _dal.Update(waitlist);
                    }
                    if (addList.Any())
                    {
                        await _sapPackOrderdal.Add(addList);
                    }
                    if (updateList.Any())
                    {
                        await _sapPackOrderdal.Update(updateList);
                    }
                    if (addProdList.Any())
                    {
                        await _proOrderdal.Add(addProdList);
                    }
                    if (updateList.Any())
                    {
                        await _proOrderdal.Update(updateProdList);
                    }
                    if (deleteProdList.Any())
                    {
                        await _proOrderdal.DeleteByIds(deleteProdList.Select(a => a.ID).ToArray());
                    }
                    if (newMvList.Count > 0)
                    {
                        await _materiaVersionlDal.Add(newMvList);
                    }
                    if (orderrealtionEntities.Count > 0)
                    {
                        await _orderrelationDal.Add(orderrealtionEntities);
                    }


                    _unitOfWork.CommitTran();
                    result.msg = $"本次接口工单:{waitlist.Count}";

                    SerilogServer.LogDebug($"新增SapPackOrder:{addList.Count}", logName);
                    SerilogServer.LogDebug($"更新SapPackOrder:{updateList.Count}", logName);
                    SerilogServer.LogDebug($"新增ProductionOrder:{addProdList.Count}", logName);
                    SerilogServer.LogDebug($"更新ProductionOrder:{updateList.Count}", logName);
                    SerilogServer.LogDebug($"删除ProductionOrder:{deleteProdList.Count}", logName);
                    SerilogServer.LogDebug($"新增物料版本:{newMvList.Count}", logName);
                    SerilogServer.LogDebug($"新增配方与灌包工单关系:{newMvList.Count}", logName);
                    SerilogServer.LogDebug($"本次接口工单:{waitlist.Count},处理工单信息结束", logName);
                }
                catch (Exception ex)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = ex.ToString();
                    SerilogServer.LogDebug($"处理工单信息异常：【{ex.ToString()}】", logName);
                    return result;
                }

                var orderNoList = waitlist.Select(m => m.Aufnr).ToList();
                await _bomService.GetSapOrderBom(new SapRequestModel
                {
                    factory = "2010",
                    orderNo = orderNoList,
                    type = "PK",
                    start = new DateTime(2024, 1, 1),
                    end = new DateTime(2099, 1, 1)
                });

                SerilogServer.LogDebug($"获取工单BOM信息,工单数量[{orderNoList.Count}]处理结束", logName);
                await _sapPackService.GetOrderRouting(new SapRequestModel
                {
                    factory = "2010",
                    orderNo = orderNoList,
                    type = "ZXH1",
                    start = new DateTime(2024, 1, 1),
                    end = new DateTime(2099, 1, 1)
                });
                SerilogServer.LogDebug($"获取工单Routing信息,工单数量[{orderNoList.Count}],处理结束", logName);
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug($"异常：【{ex.ToString()}】", logName);
                result.msg = ex.ToString();
            }

            return result;
        }
    }
}
using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using System.Collections.Generic;

namespace SEFA.PPM.Model.ViewModels.PTM
{
    public class SendBatchModel
    {
        public SendBatchModel()
        {
        }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchId { get; set; }
        /// <summary>
        /// Desc:配方组
        /// Default:
        /// Nullable:False
        /// </summary>
        public int FormulaGroup { get; set; }
        /// <summary>
        /// Desc:配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string FormulaName { get; set; }
        /// <summary>
        /// Desc:产线号
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:计划量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PlanQty { get; set; }
       
        /// <summary>
        /// Desc:计划开始时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime PlanStartTime { get; set; }
        /// <summary>
        /// Desc:计划结束时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime PlanEndTime { get; set; }
      
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// Desc:处理状态 E: 预约批次，D：取消预约
        /// Default:
        /// Nullable:True
        /// </summary>
        public string State { get; set; }
        /// <summary>
        /// Desc:关键参数列表
        /// Default:
        /// Nullable:True
        /// </summary>
        public List<ParameterModel> KeyParameterList { get; set; }
        /// <summary>
        /// Desc:Unit执行列表
        /// Default:
        /// Nullable:True
        /// </summary>
        public List<UnitExecuteModel> UnitExecuteList { get; set; }

    }

    public class UnitExecuteModel
    {
        /// <summary>
        /// Unit名称
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Unit计划量
        /// </summary>
        public decimal UnitPlanQty { get; set; }
        /// <summary>
        /// Unit启动时间
        /// </summary>
        public DateTime UnitStartTime { get; set; }
        /// <summary>
        /// Unit结束时间
        /// </summary>
        public DateTime UnitEndTime { get; set; }
    }

    public class ParameterModel
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 参数值
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 数据块
        /// </summary>
        public string DataBlock { get; set; }
        /// <summary>
        /// 数据Item
        /// </summary>
        public string Item { get; set; }
    }
    
}
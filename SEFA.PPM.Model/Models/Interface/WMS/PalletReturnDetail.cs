using System.ComponentModel.DataAnnotations;

namespace SEFA.PPM.Model.Models.Interface.WMS;

/// <summary>
/// 组托退库明细
/// </summary>
public class PalletReturnDetail
{
    /// <summary>
    /// 物料标签
    /// </summary>
    [Required]
    public string BarCode { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    [Required]
    public string MaterialCode { get; set; }

    /// <summary>
    /// 物料描述
    /// </summary>
    public string MaterialName { get; set; }

    /// <summary>
    /// 物料版本号
    /// </summary>
    public string MaterialVersionCode { get; set; }

    /// <summary>
    /// 退库数量
    /// </summary>
    public decimal Quantity { get; set; }
}
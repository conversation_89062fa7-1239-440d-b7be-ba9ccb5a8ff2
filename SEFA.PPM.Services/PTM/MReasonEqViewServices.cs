
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class MReasonEqViewServices : BaseServices<MReasonEqViewEntity>, IMReasonEqViewServices
    {
        private readonly IBaseRepository<MReasonEqViewEntity> _dal;
        public MReasonEqViewServices(IBaseRepository<MReasonEqViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<MReasonEqViewEntity>> GetList(MReasonEqViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MReasonEqViewEntity>()
							 .AndIF(!string.IsNullOrEmpty(reqModel.Key), a =>
							 (a.PlcCodeDescription != null && a.PlcCodeDescription.Contains(reqModel.Key)) ||
							 (a.ReasonDescription != null && a.ReasonDescription.Contains(reqModel.Key))||
                             (a.GroupDescription != null &&a.GroupDescription.Contains(reqModel.Key)) ||
							 (a.CategoryDescription != null && a.CategoryDescription.Contains(reqModel.Key))
							 )
							 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId),p=>p.EquipmentId.Equals(reqModel.EquipmentId))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<MReasonEqViewEntity>> GetPageList(MReasonEqViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MReasonEqViewEntity>()
				             .AndIF(!string.IsNullOrEmpty(reqModel.Key), a =>
							 (a.PlcCodeDescription != null && a.PlcCodeDescription.Contains(reqModel.Key)) ||
							 (a.ReasonDescription != null && a.ReasonDescription.Contains(reqModel.Key)) ||
							 (a.GroupDescription != null && a.GroupDescription.Contains(reqModel.Key)) ||
							 (a.CategoryDescription != null && a.CategoryDescription.Contains(reqModel.Key))
							 )
							 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), p => p.EquipmentId.Equals(reqModel.EquipmentId))
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(MReasonEqViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}
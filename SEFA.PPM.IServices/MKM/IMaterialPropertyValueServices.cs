using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMaterialPropertyValueServices
	/// </summary>	
    public interface IMaterialPropertyValueServices :IBaseServices<MaterialPropertyValueEntity>
	{
		Task<PageModel<MaterialPropertyValueEntity>> GetPageList(MaterialPropertyValueRequestModel reqModel);

        Task<List<MaterialPropertyValueEntity>> GetList(MaterialPropertyValueRequestModel reqModel);

		Task<bool> SaveForm(MaterialPropertyValueEntity entity);
    }
}
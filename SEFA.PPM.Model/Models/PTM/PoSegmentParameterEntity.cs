using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.DFM.Model.ViewModels;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PTM_B_PO_SEGMENT_PARAMETER")]
    public class PoSegmentParameterEntity : EntityBase
    {
        public PoSegmentParameterEntity()
        {
        }
        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:产品工序ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SAP_SEGMENT_PARAMETER_ID")]
        public string SapSegmentParameterId { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:关键参数名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_NAME")]
        public string ParameterName { get; set; }
        /// <summary>
        /// Desc:关键参数值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_VALUE")]
        public string ParameterValue { get; set; }
        /// <summary>
        /// Desc:数据块
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DATA_BLOCK")]
        public string DataBlock { get; set; }
        /// <summary>
        /// Desc:数据块点位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DATA_BLOCK_ITEM")]
        public string DataBlockItem { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

        /// <summary>
        /// Desc:设备Code
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string EquipmentCode { get; set; }
    }
}
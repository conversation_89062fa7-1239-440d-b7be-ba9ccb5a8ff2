using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class DcsMaterialTransferRequestModel : RequestPageModelBase
    {
        public DcsMaterialTransferRequestModel()
        {
        }
           /// <summary>
           /// Desc:源生产工单号
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SourceBatchId { get; set; }
           /// <summary>
           /// Desc:源设备编码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SourceUnitId { get; set; }
           /// <summary>
           /// Desc:目的地生产工单号
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DestinationBatchId { get; set; }
           /// <summary>
           /// Desc:目的地设备编码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DestinationUnitId { get; set; }
           /// <summary>
           /// Desc:目的地OperationId
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DestinationOpId { get; set; }
           /// <summary>
           /// Desc:目的地OperationName
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DestinationOpName { get; set; }
           /// <summary>
           /// Desc:转移数量
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Quantity { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }

    }
}
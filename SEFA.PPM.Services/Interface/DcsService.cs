using Abp.Domain.Entities;
using Abp.Extensions;
using Castle.Core.Internal;
using InfluxDB.Client.Core;
using Microsoft.AspNetCore.Http;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Dml;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SEFA.Base;
using SEFA.Base.Common;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.InfluxDb;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.MKM.Model.Models;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.InfluxDB;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using SEFA.PPM.Model.ViewModels.PTM;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.Intrinsics.X86;
using System.Text;
using System.Threading.Tasks;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;

namespace SEFA.PPM.Services.Interface
{
    public class DcsService : IDcsServices
    {
        private static string DCS_SEND_BATCH =
           Appsettings.app(new string[] { "DCSInterface", "DCS_SEND_BATCH" }).ObjToString();
        private static string DCS_POT_CHARGE =
            Appsettings.app(new string[] { "DCSInterface", "DCS_POT_CHARGE" }).ObjToString();
        private static string DCS_CHARGE_CONFIRM =
            Appsettings.app(new string[] { "DCSInterface", "DCS_CHARGE_CONFIRM" }).ObjToString();
        private static string DCS_SAMPLE_START =
            Appsettings.app(new string[] { "DCSInterface", "DCS_SAMPLE_START" }).ObjToString();
        private static string DCS_SAMPLE_COMPLETE =
            Appsettings.app(new string[] { "DCSInterface", "DCS_SAMPLE_COMPLETE" }).ObjToString();
        private static string DCS_SAMPLE_RESULT =
            Appsettings.app(new string[] { "DCSInterface", "DCS_SAMPLE_RESULT" }).ObjToString();

        private readonly IBaseRepository<ProductionOrderEntity> _podal;
        private readonly IBaseRepository<DcsBatchStatusEntity> _batchStatusdal;
        private readonly IBaseRepository<DcsUnitStatusEntity> _unitStatusdal;
        private readonly IBaseRepository<DcsOpStatusEntity> _opStatusdal;
        private readonly IBaseRepository<DcsChargeRequestEntity> _chargeRequestdal;
        private readonly IBaseRepository<DcsSampleRequestEntity> _sampleRequestdal;
        private readonly IBaseRepository<DcsStorageTankChargeEntity> _storageTankChargedal;
        private readonly IBaseRepository<DcsMaterialTransferEntity> _materialTransferdal;
        private readonly IBaseRepository<PoMaterialConsumeEntity> _poMaterialConsumedal;
        private readonly IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPoConsumeRequirementServices _poConsumeRequirementServices;

        public DcsService(IBaseRepository<
            ProductionOrderEntity> podal,
            IBaseRepository<DcsBatchStatusEntity> batchStatusdal,
            IBaseRepository<DcsUnitStatusEntity> unitStatusdal,
            IBaseRepository<DcsOpStatusEntity> opStatusdal,
            IBaseRepository<DcsChargeRequestEntity> chargeRequestdal,
            IBaseRepository<DcsSampleRequestEntity> sampleRequestdal,
            IBaseRepository<DcsStorageTankChargeEntity> storageTankChargedal,
            IBaseRepository<DcsMaterialTransferEntity> materialTransferdal,
            IBaseRepository<PoMaterialConsumeEntity> poMaterialConsumedal,
            IUser user,
            IUnitOfWork unitOfWork,
            IPoConsumeRequirementServices poConsumeRequirementServices
            )
        {
            _podal = podal;
            _batchStatusdal = batchStatusdal;
            _unitStatusdal = unitStatusdal;
            _opStatusdal = opStatusdal;
            _chargeRequestdal = chargeRequestdal;
            _sampleRequestdal = sampleRequestdal;
            _storageTankChargedal = storageTankChargedal;
            _materialTransferdal = materialTransferdal;
            _poMaterialConsumedal = poMaterialConsumedal;
            _unitOfWork = unitOfWork;
            _user = user;
            _poConsumeRequirementServices = poConsumeRequirementServices;
        }



        #region 发送DCS 预约批次 

        /// <summary>
        /// 发送DCS 预约批次 
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<MessageModel> SendBatch(List<SendBatchModel> list)
        {
            var logFileName = "DCS_SendBatch";
            var result = new MessageModel();
            SerilogServer.LogDebug($"本次发送DCS信息共[{list.Count}]条:内容：" + list.ToJson(), logFileName);

            try
            {
                SerilogServer.LogDebug($"调用接口DCS1：" + DCS_SEND_BATCH, logFileName);
                var apiResult = await HttpHelper.PostAsync<string>("DCS1", DCS_SEND_BATCH, null, list);
                SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), logFileName);

                if (apiResult == null)
                {
                    result.success = false;
                    result.msg = "接口调用返回为空！";
                    SerilogServer.LogDebug(result.msg, logFileName);
                    return result;
                }
                result.success = apiResult.success;
                result.msg = apiResult.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "接口调用异常：" + ex.StackTrace;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            return result;
        }

        #endregion

        #region 发送DCS 物料开始投料比对反馈

        /// <summary>
        /// 发送DCS 物料开始投料比对反馈
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> ChargeConfirm(ChargeConfirmModel model)
        {
            var logFileName = "DCS_ChargeConfirm";
            var result = new MessageModel();
            SerilogServer.LogDebug($"本次发送DCS信息内容：" + model.ToJson(), logFileName);

            try
            {
                SerilogServer.LogDebug($"调用接口DCS2：" + DCS_CHARGE_CONFIRM, logFileName);
                var apiResult = await HttpHelper.PostAsync<string>("DCS2", DCS_CHARGE_CONFIRM, null, model);
                SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), logFileName);

                if (apiResult == null)
                {
                    result.success = false;
                    result.msg = "接口调用返回为空！";
                    SerilogServer.LogDebug(result.msg, logFileName);
                    return result;
                }
                result.success = apiResult.success;
                result.msg = apiResult.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "接口调用异常：" + ex.StackTrace;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            return result;
        }

        #endregion

        #region 发送DCS POT物料投料开始结束信号

        /// <summary>
        /// 发送DCS POT物料投料开始结束信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> PotCharge(ChargeModel model)
        {
            var logFileName = "DCS_PotCharge";
            var result = new MessageModel();
            SerilogServer.LogDebug($"本次发送DCS信息内容：" + model.ToJson(), logFileName);

            try
            {
                SerilogServer.LogDebug($"调用接口DCS2：" + DCS_POT_CHARGE, logFileName);
                var apiResult = await HttpHelper.PostAsync<string>("DCS2", DCS_POT_CHARGE, null, model);
                SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), logFileName);

                if (apiResult == null)
                {
                    result.success = false;
                    result.msg = "接口调用返回为空！";
                    SerilogServer.LogDebug(result.msg, logFileName);
                    return result;
                }
                result.success = apiResult.success;
                result.msg = apiResult.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "接口调用异常：" + ex.StackTrace;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            return result;
        }

        #endregion

        #region 发送DCS 取样开始结束信号

        /// <summary>
        /// 发送DCS 取样开始结束信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> SampleStartComplete(SampleModel model)
        {
            var logFileName = "DCS_SampleStartComplete";
            var result = new MessageModel();
            SerilogServer.LogDebug($"本次发送DCS信息内容：" + model.ToJson(), logFileName);

            try
            {
                var url = string.Empty;
                if (model.SampleFlag == 1)
                {
                    url = DCS_SAMPLE_START;
                }
                else if (model.SampleFlag == 2)
                {
                    url = DCS_SAMPLE_COMPLETE;
                }
                SerilogServer.LogDebug($"调用接口DCS2：" + url, logFileName);
                var apiResult = await HttpHelper.PostAsync<string>("DCS2", url, null, model);

                SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), logFileName);

                if (apiResult == null)
                {
                    result.success = false;
                    result.msg = "接口调用返回为空！";
                    SerilogServer.LogDebug(result.msg, logFileName);
                    return result;
                }
                result.success = apiResult.success;
                result.msg = apiResult.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "接口调用异常：" + ex.StackTrace;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            return result;
        }

        #endregion

        #region 发送DCS 取样结果信息

        /// <summary>
        /// 发送DCS 取样结果信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> SampleResult(SampleResultModel model)
        {
            var logFileName = "DCS_SampleResult";
            var result = new MessageModel();
            SerilogServer.LogDebug($"本次发送DCS信息内容：" + model.ToJson(), logFileName);

            try
            {
                SerilogServer.LogDebug($"调用接口DCS2：" + DCS_SAMPLE_RESULT, logFileName);
                var apiResult = await HttpHelper.PostAsync<string>("DCS2", DCS_SAMPLE_RESULT, null, model);

                SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), logFileName);

                if (apiResult == null)
                {
                    result.success = false;
                    result.msg = "接口调用返回为空！";
                    SerilogServer.LogDebug(result.msg, logFileName);
                    return result;
                }
                result.success = apiResult.success;
                result.msg = apiResult.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
            }
            catch (Exception ex)
            {
                result.success = false;
                result.msg = "接口调用异常：" + ex.StackTrace;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            return result;
        }

        #endregion



        #region 接收工单批次状态变化

        /// <summary>
        /// 异步发送批次数据到DCS系统
        /// </summary>
        /// <param name="list">要发送的批次数据列表</param>
        /// <returns>包含操作结果的消息模型</returns>
        public async Task<MessageModel> BatchStateChange(BatchStateModel model)
        {
            var logFileName = "DCS_BatchStateChange";
            var result = new MessageModel();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"工单状态变化：" + model.ToJson(), logFileName);

            var entity = new DcsBatchStatusEntity()
            {
                BatchId = model.BatchId,
                State = model.State
            }; 
            entity.CreateCustomGuid("DCS");

            await _batchStatusdal.Add(entity);
           
            SerilogServer.LogDebug($"数据写入数据库完成", logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                result.success = false;
                result.msg = "传入BatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.State))
            {
                result.success = false;
                result.msg = "传入State为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var poResilt = await BatchIdCheck(model.BatchId);

            if(poResilt.success == false)
            {
                result.success = false;
                result.msg = poResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            var poEntity = poResilt.response;

            /*更新工单状态*/

            if (model.State.ToUpper() == "READY")
            {
                poEntity.PoStatus = "2";
            }
            if (model.State.ToUpper() == "ACTIVE")
            {
                poEntity.PoStatus = "6";
            }
            else if (model.State.ToUpper() == "COMPLETE")
            {
                poEntity.PoStatus = "3";
            }
            else if (model.State.ToUpper() == "ABORTED")
            {
                poEntity.PoStatus = "3";
            }
            try
            {
                await _podal.Update(poEntity);
                result.success = true;
                result.msg = "更新工单状态成功！";
                return result;
            }
            catch (Exception e)
            {
                result.success = false;
                result.msg = "更新工单状态失败！" + e.StackTrace;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
        }

        #endregion

        #region 接收Unit状态变化

        /// <summary>
        /// 接收Unit状态变化
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> UnitStateChange(UnitStateModel model)
        {
            var logFileName = "DCS_UnitStateChange";
            var result = new MessageModel();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"Unit状态变化：" + model.ToJson(), logFileName);
            var entity = new DcsUnitStatusEntity()
            {
                BatchId = model.BatchId,
                UnitId = model.UnitId,
                State = model.State
            };
            entity.CreateCustomGuid("DCS");
            await _unitStatusdal.Add(entity);

            SerilogServer.LogDebug($"数据写入数据库完成", logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                result.success = false;
                result.msg = "传入BatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.UnitId))
            {
                result.success = false;
                result.msg = "传入UnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.State))
            {
                result.success = false;
                result.msg = "传入State为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var poResilt = await BatchIdCheck(model.BatchId);

            if (poResilt.success == false)
            {
                result.success = false;
                result.msg = poResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var unitResilt = await UnitIdCheck(model.UnitId);

            if (unitResilt.success == false)
            {
                result.success = false;
                result.msg = unitResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            result.success = true;
            result.msg = "接收Unit状态变化成功！";
            return result;
        }

        #endregion

        #region 接收Operation状态变化

        /// <summary>
        /// 接收Operation状态变化
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> OperationStateChange(OperationStateModel model)
        {
            var logFileName = "DCS_OperationStateChange";
            var result = new MessageModel();

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            SerilogServer.LogDebug($"Operation状态变化：" + model.ToJson(), logFileName);
            var entity = new DcsOpStatusEntity()
            {
                BatchId = model.BatchId,
                UnitId = model.UnitId,
                OpId = model.OpId,
                OpName = model.OpName,
                State = model.State
            };
            entity.CreateCustomGuid("DCS");
            await _opStatusdal.Add(entity);

           
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                result.success = false;
                result.msg = "传入BatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.UnitId))
            {
                result.success = false;
                result.msg = "传入UnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpId))
            {
                result.success = false;
                result.msg = "传入OpId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpName))
            {
                result.success = false;
                result.msg = "传入OpName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.State))
            {
                result.success = false;
                result.msg = "传入State为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var poResilt = await BatchIdCheck(model.BatchId);

            if (poResilt.success == false)
            {
                result.success = false;
                result.msg = poResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var unitResilt = await UnitIdCheck(model.UnitId);

            if (unitResilt.success == false)
            {
                result.success = false;
                result.msg = unitResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            result.success = true;
            result.msg = "接收Operation状态变化成功！";
            return result;
        }

        #endregion

        #region 接收DCS投料请求

        /// <summary>
        /// 接收投料请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> ChargeRequest(ChargeModel model)
        {
            var logFileName = "DCS_ChargeRequest";
            var result = new MessageModel();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"接受数据：\n" + model.ToJson(), logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                result.success = false;
                result.msg = "传入BatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.UnitId))
            {
                result.success = false;
                result.msg = "传入UnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpId))
            {
                result.success = false;
                result.msg = "传入OpId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpName))
            {
                result.success = false;
                result.msg = "传入OpName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.MaterialName))
            {
                result.success = false;
                result.msg = "传入MaterialName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (model.Qty <= 0)
            {
                result.success = false;
                result.msg = "传入Qty异常！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            //_poConsumeRequirementServices.GeneratePoConsumeRequirement();

            var entity = new DcsChargeRequestEntity()
            {
                BatchId = model.BatchId,
                UnitId = model.UnitId,
                OpId = model.OpId,
                OpName = model.OpName,
                InputMaterialEquCode = model.InputMaterialEquCode,
                MaterialName = model.MaterialName,
                Quantity = model.Qty,
                OpState = model.OpState,
                State = "1"/*处理中，处理完成变为0*/
            };
            entity.CreateCustomGuid("DCS");

            await _chargeRequestdal.Add(entity);



            result.success = true;
            result.msg = "接受投料请求成功！";
            return result;
        }

        #endregion

        #region 接收DCS投料完成信号

        /// <summary>
        /// 接收DCS投料完成信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> ChargeComplete(ChargeModel model)
        {
            var logFileName = "DCS_ChargeComplete";
            var result = new MessageModel();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"接收数据：\n" + model.ToJson(), logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                result.success = false;
                result.msg = "传入BatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.UnitId))
            {
                result.success = false;
                result.msg = "传入UnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpId))
            {
                result.success = false;
                result.msg = "传入OpId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpName))
            {
                result.success = false;
                result.msg = "传入OpName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpState))
            {
                result.success = false;
                result.msg = "传入OpState为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
  
            var list = await _chargeRequestdal.FindList(a => 
                a.BatchId == model.BatchId && 
                a.UnitId == model.UnitId && 
                a.OpName == a.OpName && 
                a.OpId == model.OpId && 
                a.State == "1" );

            
            if(list.Count > 0)
            {
                foreach (var item in list)
                {
                    item.State = "0";
                    item.Modify(item.ID, "DCS");
                }

                await _chargeRequestdal.Update(list);
            }

            var entity = new DcsChargeRequestEntity()
            {
                BatchId = model.BatchId,
                UnitId = model.UnitId,
                OpId = model.OpId,
                OpName = model.OpName,
                InputMaterialEquCode = model.InputMaterialEquCode,
                MaterialName = model.MaterialName,
                Quantity = model.Qty,
                OpState = model.OpState,
                State = "0"/*直接处理完成*/
            };
            entity.CreateCustomGuid("DCS");

            await _chargeRequestdal.Add(entity);



            result.success = true;
            result.msg = "接收投料完成信号成功！";
            return result;
        }

        #endregion

        #region 接收DCS储罐原料加料完成信号

        /// <summary>
        /// 接收DCS储罐原料加料完成信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> StorageTankMaterialCharge(StorageTankChargeModel model)
        {
            var logFileName = "DCS_StorageTankMaterialCharge";
            var result = new MessageModel();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"接收数据：\n" + model.ToJson(), logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.SourceUnitId))
            {
                result.success = false;
                result.msg = "传入SourceUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationUnitId))
            {
                result.success = false;
                result.msg = "传入DestinationUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationUnitId))
            {
                result.success = false;
                result.msg = "传入DestinationUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationUnitId))
            {
                result.success = false;
                result.msg = "传入DestinationUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationOpName))
            {
                result.success = false;
                result.msg = "传入DestinationOpName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (model.Qty <= 0)
            {
                result.success = false;
                result.msg = "传入Qty异常！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var poResilt = await BatchIdCheck(model.DestinationBatchId);

            if (poResilt.success == false)
            {
                result.success = false;
                result.msg = poResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var dUnitResilt = await UnitIdCheck(model.DestinationBatchId);

            if (dUnitResilt.success == false)
            {
                result.success = false;
                result.msg = dUnitResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var sUnitResilt = await UnitIdCheck(model.SourceUnitId);

            if (sUnitResilt.success == false)
            {
                result.success = false;
                result.msg = sUnitResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

           
            /*1.调用WMS接口检查储罐批次信息,检查物料需求是否正确*/

            var materialList = new List<WmsInventoryModel>();

            if(materialList.Count == 0)
            {
                result.success = false;
                result.msg = $"调用WMS接口未找到储罐[{model.SourceUnitId}]库存";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            /*获取库存存储的物料信息*/
            var materialResult =await GetMaterial(materialList.First().MaterialCode);
            if (materialResult.success == false)
            {
                result.success = false;
                result.msg = materialResult.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            /*检查物料是否可以投料*/
            var bomResult = await MaterialCheckbyId(model.DestinationBatchId, model.DestinationUnitId, materialResult.response.ID, materialResult.response.NAME, model.SourceUnitId);

            if (result.success == false)
            {
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            /*检查数量是否足够扣减*/
            if (materialList.Sum(a => a.Quantity) < model.Qty)
            {
                result.success = false;
                result.msg = $"储罐[{model.SourceUnitId}]库存总量小于本次投料量[{model.Qty}]";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }




            /*2.循环储罐批次信息，并根据入库时间扣减，插入消耗表*/

            decimal remindQty = model.Qty;
            List< PoMaterialConsumeEntity > poMaterialConsumeList = new List< PoMaterialConsumeEntity >();

            foreach (var item in materialList.OrderBy(a=>a.StorageTime).OrderBy(a=>a.BatchNo))
            {
                var entity = new PoMaterialConsumeEntity();
                entity.CreateCustomGuid(_user.Name);
                entity.ProductionOrderId = model.DestinationBatchId;
                entity.PoConsumenRequirementId = bomResult.response.PoConsumeRequirementId;
                entity.MaterialBatchNo = item.BatchNo;
                //entity.Quantity = item.Quantity;
                //entity.Unit = spoMaterialentity.First().Unit;
                entity.StorageCode = model.SourceUnitId;

                if(remindQty >= item.Quantity)
                {
                    entity.Quantity = item.Quantity;
                    remindQty -= item.Quantity;
                }
                else
                {
                    entity.Quantity = remindQty;
                    remindQty = 0;
                }

                poMaterialConsumeList.Add(entity);

                if (remindQty == 0)
                {
                    break;
                }
            }


            /*3.调用WMS出库接口，扣减储罐物料*/

            await _poMaterialConsumedal.Add(poMaterialConsumeList);

            result.success = true;
            result.msg = "接收储罐原料加料完成信号成功！";
            return result;
        }

        #endregion

        #region 接收DCS工单转料完成信号

        /// <summary>
        /// 接收DCS工单转料完成信号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> MaterialTransfer(MaterialTransferModel model)
        {
            var logFileName = "DCS_MaterialTransfer";
            var result = new MessageModel();
            SerilogServer.LogDebug($"接收数据：\n" + model.ToJson(), logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.SourceBatchId))
            {
                result.success = false;
                result.msg = "传入SourceBatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.SourceUnitId))
            {
                result.success = false;
                result.msg = "传入SourceUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationUnitId))
            {
                result.success = false;
                result.msg = "传入DestinationUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationUnitId))
            {
                result.success = false;
                result.msg = "传入DestinationUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationUnitId))
            {
                result.success = false;
                result.msg = "传入DestinationUnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.DestinationOpName))
            {
                result.success = false;
                result.msg = "传入DestinationOpName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (model.Qty <= 0)
            {
                result.success = false;
                result.msg = "传入Qty异常！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }


            if (model.DestinationBatchId == model.SourceBatchId)
            {
                result.success = true;
                result.msg = "SourceBatchId与DestinationBatchId相同,不做处理！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var dPoResilt = await BatchIdCheck(model.DestinationBatchId);

            if (dPoResilt.success == false)
            {
                result.success = false;
                result.msg = dPoResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var dUnitResilt = await UnitIdCheck(model.DestinationBatchId);

            if (dUnitResilt.success == false)
            {
                result.success = false;
                result.msg = dUnitResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var sPoResilt = await BatchIdCheck(model.SourceBatchId);

            if (sPoResilt.success == false)
            {
                result.success = false;
                result.msg = sPoResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var sUnitResilt = await UnitIdCheck(model.SourceUnitId);

            if (sUnitResilt.success == false)
            {
                result.success = false;
                result.msg = sUnitResilt.msg;
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            var spoMaterialentity =  _podal.Db.Queryable<MaterialEntity>().Where(a => a.ID == sPoResilt.response.MaterialId).ToList();

            if(spoMaterialentity.Count == 0)
            {
                result.success = false;
                result.msg = $"工单[{model.SourceBatchId}]产出物料不存在";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            /*检查物料是否可以投料*/
            var bomResult = await MaterialCheckbyId(model.DestinationBatchId, model.DestinationUnitId, spoMaterialentity.First().ID, spoMaterialentity.First().NAME, model.SourceUnitId);

            if (result.success == false)
            {
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

             /*插入工单消耗表*/

            var entity = new PoMaterialConsumeEntity();
            entity.CreateCustomGuid(_user.Name);
            entity.ProductionOrderId = model.DestinationBatchId;
            entity.PoConsumenRequirementId = bomResult.response.PoConsumeRequirementId;
            entity.MaterialBatchNo = sPoResilt.response.ProductionOrderNo;
            entity.Quantity = model.Qty;
            entity.Unit = spoMaterialentity.First().Unit;
            entity.StorageCode = model.SourceUnitId;

            await _poMaterialConsumedal.Add(entity);



            result.success = true;
            result.msg = "接收工单转料完成信号成功！";
            return result;
        }

        #endregion

        #region 接收DCS取样请求

        /// <summary>
        /// 接收DCS 取样请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel> SampleRequest(SampleRequestModel model)
        {
            var logFileName = "DCS_SampleRequest";
            var result = new MessageModel();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"接受数据：\n" + model.ToJson(), logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                result.success = false;
                result.msg = "传入BatchId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.UnitId))
            {
                result.success = false;
                result.msg = "传入UnitId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpId))
            {
                result.success = false;
                result.msg = "传入OpId为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.OpName))
            {
                result.success = false;
                result.msg = "传入OpName为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            if (string.IsNullOrWhiteSpace(model.Message))
            {
                result.success = false;
                result.msg = "传入Message为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }

            result.success = true;
            result.msg = "接受投料请求成功！";
            return result;
        }

        #endregion


        #region 扫描设备投料口，获取需要投料的信息

        /// <summary>
        /// 接收DCS 取样请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<MessageModel<DcsChargeRequestEntity>> ScanInputMaterialEquipmentCode(DcsChargeRequestModel model)
        {
            var logFileName = "ScanInputMaterialEquipmentCode";
            var result = new MessageModel<DcsChargeRequestEntity>();
            // 记录调试日志，包含本次下发的批次数量和内容
            SerilogServer.LogDebug($"接受数据：\n" + model.ToJson(), logFileName);

            if (model == null)
            {
                result.success = false;
                result.msg = "传入信息为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
          
            if (string.IsNullOrWhiteSpace(model.InputMaterialEquCode))
            {
                result.success = false;
                result.msg = "传入投料口为空！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            var datetime = DateTime.Now.AddMinutes(-60);

            var requestList = await _chargeRequestdal.FindList(a => a.InputMaterialEquCode == model.InputMaterialEquCode && a.CreateDate > datetime,a=>a.CreateDate,false);
            if(requestList.Count == 0)
            {
                result.success = false;
                result.msg = $"未收到投料口[{model.InputMaterialEquCode}]投料请求！";
                SerilogServer.LogDebug(result.msg, logFileName);
                return result;
            }
            var curRequestEntity = requestList.OrderByDescending(a => a.CreateDate).First();

            SerilogServer.LogDebug($"返回数据：\n" + curRequestEntity.ToJson(), logFileName);
            result.success = true;
            result.response = curRequestEntity;
            result.msg = "获取数据成功！";
            return result;
        }

        #endregion


        #region 汇总工单DCS信号

        public async Task<List<DcsMsgModel>> GetPoDcsInfo(BatchStateModel model)
        {

            List<DcsMsgModel> list = new List<DcsMsgModel>();

            var poEntity = await _podal.FindEntity (model.BatchId);

            if(poEntity == null)
            {
                return list;
            }
            model.BatchId = poEntity.ProductionOrderNo;

            var batchStatusList = await _batchStatusdal.FindList(a => a.BatchId == model.BatchId);

            foreach (var item in batchStatusList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.BatchId;
                dcsMsgModel.Type = "批次状态变更";
                dcsMsgModel.State = item.State;
                dcsMsgModel.MsgTime = item.CreateDate;

                list.Add(dcsMsgModel);
            }

            var untiStatusList  = await _unitStatusdal.FindList(a => a.BatchId == model.BatchId);

            foreach (var item in untiStatusList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.BatchId;
                dcsMsgModel.Type = "设备状态变更";
                dcsMsgModel.UnitId = item.UnitId;
                dcsMsgModel.State = item.State;
                dcsMsgModel.MsgTime = item.CreateDate;

                list.Add(dcsMsgModel);
            }

            var opStatusList = await _opStatusdal.FindList(a => a.BatchId == model.BatchId);

            foreach (var item in opStatusList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.BatchId;
                dcsMsgModel.Type = "设备状态变更";
                dcsMsgModel.UnitId = item.UnitId;
                dcsMsgModel.State = item.State;
                dcsMsgModel.OpId = item.OpId;
                dcsMsgModel.OpName = item.OpName;
                dcsMsgModel.MsgTime = item.CreateDate;

                list.Add(dcsMsgModel);
            }

            var chargeList = await _chargeRequestdal.FindList(a => a.BatchId == model.BatchId);

            foreach (var item in chargeList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.BatchId;
                dcsMsgModel.Type = "DCS投料请求";
                dcsMsgModel.UnitId = item.UnitId;
                dcsMsgModel.OpId = item.OpId;
                dcsMsgModel.OpName = item.OpName;
                dcsMsgModel.MsgTime = item.CreateDate;
                dcsMsgModel.InputMaterialEquCode = item.InputMaterialEquCode;
                dcsMsgModel.MaterialName = item.MaterialName;
                dcsMsgModel.Quantity = item.Quantity;
             
                list.Add(dcsMsgModel);
            }


            var storageTankMaterialChargeList = await _storageTankChargedal.FindList(a => a.DestinationBatchId == model.BatchId);

            foreach (var item in storageTankMaterialChargeList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.DestinationBatchId;
                dcsMsgModel.Type = "DCS储罐抽料";
                dcsMsgModel.UnitId = item.DestinationUnitId;
                dcsMsgModel.OpId = item.DestinationOpId;
                dcsMsgModel.OpName = item.DestinationOpName;
                dcsMsgModel.MsgTime = item.CreateDate;
                dcsMsgModel.StorageTank = item.SourceUnitId;
                dcsMsgModel.Quantity = item.Quantity;

                list.Add(dcsMsgModel);
            }

            var materialTransferList = await _materialTransferdal.FindList(a =>  a.DestinationBatchId == model.BatchId && a.SourceUnitId != a.DestinationBatchId);

            foreach (var item in storageTankMaterialChargeList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.DestinationBatchId;
                dcsMsgModel.Type = "DCS转料";
                dcsMsgModel.UnitId = item.DestinationUnitId;
                dcsMsgModel.OpId = item.DestinationOpId;
                dcsMsgModel.OpName = item.DestinationOpName;
                dcsMsgModel.MsgTime = item.CreateDate;
                dcsMsgModel.StorageTank = item.SourceUnitId;
                dcsMsgModel.Quantity = item.Quantity;

                list.Add(dcsMsgModel);
            }

            var sampleRequestList = await _sampleRequestdal.FindList(a => a.BatchId == model.BatchId);

            foreach (var item in sampleRequestList)
            {
                DcsMsgModel dcsMsgModel = new DcsMsgModel();
                dcsMsgModel.BatchId = item.BatchId;
                dcsMsgModel.Type = "取样";
                dcsMsgModel.UnitId = item.UnitId;
                dcsMsgModel.OpId = item.OpId;
                dcsMsgModel.OpName = item.OpName;
                dcsMsgModel.MsgTime = item.CreateDate;
                dcsMsgModel.Message = item.Message;

                list.Add(dcsMsgModel);
            }

            list.OrderBy(a => a.MsgTime).ToList();

            return list;
        }

        #endregion

        #region 通用方法

        #region 检查BatchId
        /// <summary>
        /// 检查BatchId
        /// </summary>
        /// <param name="batchId"></param>
        /// <returns>返回工单实体</returns>
        public async Task<MessageModel<ProductionOrderEntity>> BatchIdCheck(string  batchId)
        {
            var result = new MessageModel<ProductionOrderEntity>();
            var poList = await _podal.FindList(a => a.ProductionOrderNo == batchId);
            if (poList == null || poList.Count == 0)
            {
                result.success = false;
                result.msg = "传入BatchId不存在！";
                return result;
            }

            if (poList.Count > 1)
            {
                result.success = false;
                result.msg = $"BatchId[{batchId}]存在多个,数据异常！";
                return result;
            }
            result.response = poList[0];
            result.success = true;
            return result;
        }
        #endregion

        #region 检查UnitId
        /// <summary>
        /// 检查UnitId
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns>返回设备实体</returns>
        public async Task<MessageModel<DFM.Model.Models.EquipmentEntity>> UnitIdCheck(string unitId)
        {
            var result = new MessageModel<EquipmentEntity>();
            var equipmentList = await _podal.Db.Queryable<DFM.Model.Models.EquipmentEntity>().Where(x => x.EquipmentCode == unitId).ToListAsync();
            if (equipmentList == null || equipmentList.Count == 0)
            {
                result.success = false;
                result.msg = $"传入unitId[{unitId}]不存在！";
                return result;
            }

            if (equipmentList.Count > 1)
            {
                result.success = false;
                result.msg = $"传入UnitId[{unitId}]存在多个,数据异常！";
                return result;
            }
            equipmentList =  equipmentList.Where(a => a.Level == "Unit").ToList();

            if (equipmentList == null || equipmentList.Count == 0)
            {
                result.success = false;
                result.msg = $"传入unitId[{unitId}]类型异常！";
                return result;
            }
            result.response = equipmentList[0];
            result.success = true;
            return result;
        }
        #endregion

        #region 检查UnitId
        /// <summary>
        /// 检查UnitId
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns>返回设备实体</returns>
        public async Task<MessageModel<DFM.Model.Models.MaterialEntity>> GetMaterial(string materialCode)
        {
            var result = new MessageModel<MaterialEntity>();
            var materialList = await _podal.Db.Queryable<DFM.Model.Models.MaterialEntity>().Where(x => x.Code == materialCode).ToListAsync();
            if (materialList == null || materialList.Count == 0)
            {
                result.success = false;
                result.msg = $"物料编码[{materialCode}]不存在！";
                return result;
            }

            if (materialList.Count > 1)
            {
                result.success = false;
                result.msg = $"物料编码[{materialCode}]存在多个,数据异常！";
                return result;
            }
          
            result.response = materialList[0];
            result.success = true;
            return result;
        }
        #endregion

        #region 检查物料是否可在工单，设备，投料口投料
        /// <summary>
        /// 检查物料是否可在工单，设备，投料口投料
        /// </summary>
        /// <param name="batchId">工单ID</param>
        /// <param name="unitId">设备编码</param>
        /// <param name="materialName">物料名称</param>
        /// <param name="feedport">投料口</param>
        /// <returns></returns>
        public async Task<MessageModel<BomMaterialModel>> MaterialCheck(string batchId, string unitId, string materialName, string feedport = null)
        {
            var result = new MessageModel<BomMaterialModel>();
            var poConsumeRequirementEntities = await _podal.Db.Queryable<PoConsumeRequirementEntity>().Where(x => x.ProductionOrderId == batchId).ToListAsync();
            if (poConsumeRequirementEntities == null || poConsumeRequirementEntities.Count == 0)
            {
                result.success = false;
                result.msg = $"未找到工单[{batchId}]物料需求信息！";
                return result;
            }

            var materialList = await _podal.Db.Queryable<
                            PoSegmentRequirementEntity,
                            PoConsumeRequirementEntity,
                            SapSegmentMaterialStepEntity,
                            SapSegmentMaterialEntity,
                            DFM.Model.Models.MaterialEntity,
                            SapSegmentEquipmentEntity,
                            DFM.Model.Models.EquipmentEntity>(
                       (psr, pcr, ssms, ssm, m, sse, e) => new object[]
                       {
                            JoinType.Inner, psr.ID == pcr.PoSegmentRequirementId,
                            JoinType.Inner,pcr.BomMaterialId == ssms.ID ,
                            JoinType.Inner, ssms.SapSegmentMaterialId == ssm.ID,
                            JoinType.Inner, ssms.MaterialId == m.ID,
                            JoinType.Inner, ssm.SapSegmentId == sse.SapSegmentId,
                            JoinType.Inner, sse.EquipmentId == e.ID
                       })
                       .Where((psr, pcr, ssms, ssm, m, sse, e)=> pcr.ProductionOrderId == batchId && e.EquipmentCode == unitId && m.Description == materialName )
                       .Select((psr, pcr, ssms, ssm, m, sse, e) => new BomMaterialModel
                       {
                           MaterialId = ssms.MaterialId,
                           BomMaterialId = ssms.ID,
                           StorageCode = e.EquipmentCode,
                           StandQty = ssms.ParentQuantity,
                           Quantity = ssms.Quantity,
                           FeedPort = ssms.FeedPort,
                           PoConsumeRequirementId = pcr.ID
                       }).ToListAsync();

            if (materialList == null || materialList.Count == 0)
            {
                result.success = false;
                result.msg = $"工单[{batchId}]在设备[{unitId}]不允许投入物料[{materialName}]！";
                return result;
            }

            if (materialList.Count >1)
            {
                result.success = false;
                result.msg = $"工单[{batchId}]在设备[{unitId}]投入物料[{materialName}]存在多条数据，清检查配置！";
                return result;
            }
            var materialEntity = materialList[0];
            if(!string.IsNullOrEmpty(feedport))
            {
                if (!string.IsNullOrEmpty(materialEntity.FeedPort) && materialEntity.FeedPort.Contains(feedport))
                {
                    result.success = false;
                    result.msg = $"工单[{batchId}]投入物料[{materialName}]不可在设备[{unitId}]投料口[{feedport}]投料！";
                    return result;
                }
            }
            result.response = materialEntity;
            result.success = true;
            return result;
        }
        #endregion

        #region 检查物料是否可在工单，设备，投料口投料
        /// <summary>
        /// 检查物料是否可在工单，设备，投料口投料
        /// </summary>
        /// <param name="batchId">工单ID</param>
        /// <param name="unitId">设备编码</param>
        /// <param name="materialId">物料Id</param>
        /// <param name="materialName">物料名称</param>
        /// <param name="feedport">投料口</param>
        /// <returns></returns>
        public async Task<MessageModel<BomMaterialModel>> MaterialCheckbyId(string batchId, string unitId, string materialId, string materialName, string feedport = null)
        {
            var result = new MessageModel<BomMaterialModel>();
            var poConsumeRequirementEntities = await _podal.Db.Queryable<PoConsumeRequirementEntity>().Where(x => x.ProductionOrderId == batchId).ToListAsync();
            if (poConsumeRequirementEntities == null || poConsumeRequirementEntities.Count == 0)
            {
                result.success = false;
                result.msg = $"未找到工单[{batchId}]物料需求信息！";
                return result;
            }

            var materialList = await _podal.Db.Queryable<
                            PoSegmentRequirementEntity,
                            PoConsumeRequirementEntity,
                            SapSegmentMaterialStepEntity,
                            SapSegmentMaterialEntity,
                            DFM.Model.Models.MaterialEntity,
                            SapSegmentEquipmentEntity,
                            DFM.Model.Models.EquipmentEntity>(
                       (psr, pcr, ssms, ssm, m, sse, e) => new object[]
                       {
                            JoinType.Inner, psr.ID == pcr.PoSegmentRequirementId,
                            JoinType.Inner,pcr.BomMaterialId == ssms.ID ,
                            JoinType.Inner, ssms.SapSegmentMaterialId == ssm.ID,
                            JoinType.Inner, ssms.MaterialId == m.ID,
                            JoinType.Inner, ssm.SapSegmentId == sse.SapSegmentId,
                            JoinType.Inner, sse.EquipmentId == e.ID
                       })
                       .Where((psr, pcr, ssms, ssm, m, sse, e) => pcr.ProductionOrderId == batchId && e.EquipmentCode == unitId && m.ID == materialId)
                       .Select((psr, pcr, ssms, ssm, m, sse, e) => new BomMaterialModel
                       {
                           MaterialId = ssms.MaterialId,
                           BomMaterialId = ssms.ID,
                           StorageCode = e.EquipmentCode,
                           StandQty = ssms.ParentQuantity,
                           Quantity = ssms.Quantity,
                           FeedPort = ssms.FeedPort,
                           PoConsumeRequirementId = pcr.ID
                       }).ToListAsync();

            if (materialList == null || materialList.Count == 0)
            {
                result.success = false;
                result.msg = $"工单[{batchId}]在设备[{unitId}]不允许投入当前物料[{materialName}]！";
                return result;
            }

            if (materialList.Count > 1)
            {
                result.success = false;
                result.msg = $"工单[{batchId}]在设备[{unitId}]投入物料[{materialName}]存在多条数据，清检查配置！";
                return result;
            }
            var materialEntity = materialList[0];
            if (!string.IsNullOrEmpty(feedport))
            {
                if (!string.IsNullOrEmpty(materialEntity.FeedPort) && materialEntity.FeedPort.Contains(feedport))
                {
                    result.success = false;
                    result.msg = $"工单[{batchId}]投入物料[{materialName}]不可在设备[{unitId}]投料口[{feedport}]投料！";
                    return result;
                }
            }

            result.success = true;
            return result;
        }
        #endregion


        #region 检查物料是否可在工单，设备，投料口投料
  /// <summary>
  /// 获取工单物料
  /// </summary>
  /// <param name="batchId"></param>
  /// <param name="materialName"></param>
  /// <param name="feedport"></param>
  /// <returns></returns>
        public async Task<MessageModel<BomMaterialModel>> GetBomMaterial(string batchId, string materialName, string feedport = null)
        {
            var result = new MessageModel<BomMaterialModel>();
            var poConsumeRequirementEntities = await _podal.Db.Queryable<PoConsumeRequirementEntity>().Where(x => x.ProductionOrderId == batchId).ToListAsync();
            if (poConsumeRequirementEntities == null || poConsumeRequirementEntities.Count == 0)
            {
                result.success = false;
                result.msg = $"未找到工单[{batchId}]物料需求信息！";
                return result;
            }

            var materialList = await _podal.Db.Queryable<
                            ProductionOrderEntity,
                            SapSegmentMaterialEntity,
                            SapSegmentMaterialStepEntity,
                            DFM.Model.Models.MaterialEntity>(
                       (p, ssm ,ssms, m) => new object[]
                       {
                            JoinType.Inner, p.MaterialVersionId == ssm.MaterialVersionId,
                            JoinType.Inner, ssm.ID == ssms.SapSegmentMaterialId ,
                            JoinType.Inner, ssms.MaterialId == m.ID
                       })
                       .Where((p, ssm, ssms, m) => p.ProductionOrderNo == batchId && m.Description == materialName && ssms.FeedPort != null )
                       .Select((p, ssm, ssms, m) => new BomMaterialModel
                       {
                           SapSegmentId = ssm.SapSegmentId,
                           MaterialId = ssms.MaterialId,
                           BomMaterialId = ssms.ID,
                           StandQty = ssms.ParentQuantity,
                           Quantity = ssms.Quantity,
                           FeedPort = ssms.FeedPort
                       }).ToListAsync();

            if (materialList == null || materialList.Count == 0)
            {
                result.success = false;
                result.msg = $"工单[{batchId}]BOM不存在物料[{materialName}]！";
                return result;
            }

            if (materialList.Count > 1)
            {
                result.success = false;
                result.msg = $"工单[{batchId}]投入物料[{materialName}]存在多条数据，请检查配置！";
                return result;
            }
            var materialEntity = materialList[0];
            if (!string.IsNullOrEmpty(feedport))
            {
                if (!string.IsNullOrEmpty(materialEntity.FeedPort) && materialEntity.FeedPort.Contains(feedport))
                {
                    result.success = false;
                    result.msg = $"工单[{batchId}]投入物料[{materialName}]不可在投料口[{feedport}]投料，应在[{materialEntity.FeedPort}]！";
                    return result;
                }
            }

            result.success = true;
            return result;
        }
        #endregion


        public class BomMaterialModel
        {
            public string PoConsumeRequirementId { get; set; }
            public string MaterialId { get; set; }
            public string BomMaterialId { get; set; }
            public string StorageCode { get; set; }
            public decimal StandQty { get; set; }
            public decimal Quantity { get; set; }
            public string FeedPort { get; set; }
            public string SapSegmentId { get; internal set; }
        }

        #endregion
    }
}

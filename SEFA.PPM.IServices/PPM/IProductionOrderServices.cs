using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IProductionOrderServices
	/// </summary>	
    public interface IProductionOrderServices :IBaseServices<ProductionOrderEntity>
	{
		Task<PageModel<ProductionOrderEntity>> GetPageList(ProductionOrderRequestModel reqModel);

        Task<List<ProductionOrderEntity>> GetList(ProductionOrderRequestModel reqModel);
		Task<MessageModel<string>> BatchEditOrderRemark(OrderRemarkChangeModel req);

        Task<bool> SaveForm(ProductionOrderEntity entity);

		Task<MessageModel<string>> CalculateSegementRequirement (string PoId);

		Task<List<OrderTextResponse>> GetOrderText(OrderTextRequest reqModel);

		Task<MessageModel<string>> ComparePoLongText (List<string> poIds = null);

    }
}
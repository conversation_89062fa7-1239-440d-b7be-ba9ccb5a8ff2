using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class DcsSampleRequestRequestModel : RequestPageModelBase
    {
        public DcsSampleRequestRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:设备
           /// Default:
           /// Nullable:False
           /// </summary>
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:OPID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string OpId { get; set; }
           /// <summary>
           /// Desc:Op名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string OpName { get; set; }
           /// <summary>
           /// Desc:信息
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Message { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }

    }
}
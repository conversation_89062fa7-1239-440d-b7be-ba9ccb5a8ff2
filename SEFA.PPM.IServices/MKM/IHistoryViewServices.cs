using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels.View;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IHistoryViewServices
	/// </summary>	
    public interface IHistoryViewServices :IBaseServices<HistoryViewEntity>
	{
		Task<PageModel<HistoryViewEntityModel>> GetPageList(HistoryViewRequestModel reqModel);

        Task<List<ConsumModels>> GetExportList(HistoryViewRequestModel reqModel);

        Task<List<Select>> GetConsumMachine(BatchPalletModel reqModel);
		 Task<List<Select>> GetConsumMachineSource(BatchPalletModel reqModel);
        Task<List<HistoryViewEntity>> GetList(HistoryViewRequestModel reqModel);

		Task<bool> SaveForm(HistoryViewEntity entity);

		Task<decimal> GetConsumedQTY(string processOrder, string m_Code, string proBatch, string sscc, string machineCode, string sourceCode);

        Task<MessageModel<string>> GetGetConsumedQTY(ReverseModel reqModel);
        
        Task<MessageModel<string>> Recoil(string id, decimal quantity);

		Task<MessageModel<string>> RepeatPlan(string[] id);

		Task<List<GroupData>> GetConsumeSumList(HistoryViewRequestModel reqModel);
	}
}
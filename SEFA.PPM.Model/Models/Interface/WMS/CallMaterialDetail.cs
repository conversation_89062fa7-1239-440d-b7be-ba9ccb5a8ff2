using System;

namespace SEFA.PPM.Model.Models.Interface.WMS;

/// <summary>
/// 请求明细模型
/// </summary>
public class CallMaterialDetail
{
    /// <summary>
    /// 明细号
    /// </summary>
    public int DetailsNumber { get; set; }
    
    /// <summary>
    /// 工厂
    /// </summary>
    public string Plant { get; set; }
    
    /// <summary>
    /// 物料编码
    /// </summary>
    public string MaterialCode { get; set; }
    
    /// <summary>
    /// 物料名称
    /// </summary>
    public string MaterialName { get; set; }
    
    /// <summary>
    /// 物料版本号
    /// </summary>
    public string MaterialVersionCode { get; set; }
    
    /// <summary>
    /// 请求数量
    /// </summary>
    public decimal RequestQty { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }
    
    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNo { get; set; }
    
    /// <summary>
    /// 托盘号
    /// </summary>
    public string PalletNo { get; set; }
}
using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models.PTM
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("V_PTM_COOK_CONFIRMATION_LIST")]
    public class CookConfirmationListEntity : EntityBase
    {
        public CookConfirmationListEntity()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ORDER_ID")]
        public string OrderId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "StART_TIME")]
        public DateTime StartTime { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "END_TIME")]
        public DateTime EndTime { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DURATION")]
        public decimal Duration { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RUNTIME_HOURS")]
        public decimal RuntimeHours { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REASON")]
        public string Reason { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_NO")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VERSION_ID")]
        public string MaterialVersionId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_QTY")]
        public decimal? PlanQty { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SPEED")]
        public int? Speed { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SPEED_UOM")]
        public string SpeedUom { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BOM_VERSION")]
        public string BomVersion { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_START_TIME")]
        public DateTime? PlanStartTime { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_END_TIME")]
        public DateTime? PlanEndTime { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PO_STATUS")]
        public int? PoStatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RELEASE_STATUS")]
        public int? ReleaseStatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FINAL_STATUS")]
        public int? FinalStatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CAPICITY")]
        public int? Capicity { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EXPECTED_EFFICIENCY")]
        public decimal? ExpectedEfficiency { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_NAME")]
        public string ShiftName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EXECUTION_COUNT")]
        public int ExecutionCount { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RUNNING_COUNT")]
        public int RunningCount { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STOPED_COUNT")]
        public int StopedCount { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_COUNT")]
        public int BatchCount { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_DESCRIPTION")]
        public string MaterialDescription { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VERSION_NUMBER")]
        public string MaterialVersionNumber { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESOURCE")]
        public string Resource { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "NEED_QA_RELEASE")]
        public string NeedQaRelease { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_QTY")]
        public decimal ActualQty { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_CODE")]
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "QASTATUS")]
        public string Qastatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Formula { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAPORDERTYPE")]
        public string Sapordertype { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public long? Sequence { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SendWcs")]
        public string Sendwcs { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_CODE")]
        public string SegmentCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsHavePreservative")]
        public string Ishavepreservative { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Grille { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS")]
		public int Status { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SAPDATE")]
		public DateTime? SapDate { get; set; }
		/// <summary>
		/// Desc:顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "FormulaSequence")]
		public int FormulaSequence { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG")]
		public string Msg { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SENDTIME")]
		public DateTime? SendTime { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SENDTIME2")]
		public DateTime? SendTime2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS2")]
		public int Status2 { get; set; }
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_NO")]
		public string CONF_NO { get; set; }
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_CNT")]
		public string CONF_CNT { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "CONF_TEXT")]
		public string CONF_TEXT { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG2")]
		public string Msg2 { get; set; }
	}
}
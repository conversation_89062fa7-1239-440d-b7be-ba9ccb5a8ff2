using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PTM_B_DCS_MATERIAL_TRANSFER")] 
    public class DcsMaterialTransferEntity : EntityBase
    {
        public DcsMaterialTransferEntity()
        {
        }
           /// <summary>
           /// Desc:源生产工单号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SOURCE_BATCH_ID")]
        public string SourceBatchId { get; set; }
           /// <summary>
           /// Desc:源设备编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SOURCE_UNIT_ID")]
        public string SourceUnitId { get; set; }
           /// <summary>
           /// Desc:目的地生产工单号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESTINATION_BATCH_ID")]
        public string DestinationBatchId { get; set; }
           /// <summary>
           /// Desc:目的地设备编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESTINATION_UNIT_ID")]
        public string DestinationUnitId { get; set; }
           /// <summary>
           /// Desc:目的地OperationId
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESTINATION_OP_ID")]
        public string DestinationOpId { get; set; }
           /// <summary>
           /// Desc:目的地OperationName
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESTINATION_OP_NAME")]
        public string DestinationOpName { get; set; }
           /// <summary>
           /// Desc:转移数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public string Quantity { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

    }
}
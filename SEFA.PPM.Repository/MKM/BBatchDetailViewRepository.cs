using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// BBatchDetailViewRepository
	/// </summary>
    public class BBatchDetailViewRepository : BaseRepository<BBatchDetailViewEntity>, IBBatchDetailViewRepository
    {
        public BBatchDetailViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}
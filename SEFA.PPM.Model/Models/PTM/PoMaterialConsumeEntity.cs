using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PTM_B_PO_MATERIAL_CONSUME")] 
    public class PoMaterialConsumeEntity : EntityBase
    {
        public PoMaterialConsumeEntity()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:工单消耗需求ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PO_CONSUMEN_REQUIREMENT_ID")]
        public string PoConsumenRequirementId { get; set; }
           /// <summary>
           /// Desc:物料批次
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName= "MATERIAL_BATCH_NO")]
        public string MaterialBatchNo { get; set; }
           /// <summary>
           /// Desc:消耗子批次
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_SUB_BATCH_NO")]
        public string MaterialSubBatchNo { get; set; }
           /// <summary>
           /// Desc:库存位置
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STORAGE_CODE")]
        public string StorageCode { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="UNIT")]
        public string Unit { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATE")]
        public string State { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
           /// <summary>
           /// Desc:备料单ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SHEET_CONTAINER_ID")]
        public string SheetContainerId { get; set; }

    }
}
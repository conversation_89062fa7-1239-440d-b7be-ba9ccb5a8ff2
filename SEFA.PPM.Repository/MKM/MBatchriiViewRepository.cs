using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// MBatchriiViewRepository
	/// </summary>
    public class MBatchriiViewRepository : BaseRepository<MBatchriiViewEntity>, IMBatchriiViewRepository
    {
        public MBatchriiViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}
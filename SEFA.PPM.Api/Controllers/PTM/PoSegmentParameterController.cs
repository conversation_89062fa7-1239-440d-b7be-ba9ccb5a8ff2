using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Services;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
	[Route("ptm/[controller]/[action]")]
	[ApiController]
    [AllowAnonymous]
    public class PoSegmentParameterController : BaseApiController
    {
        /// <summary>
        /// PoSegmentParameter
        /// </summary>
        private readonly IPoSegmentParameterServices _poSegmentParameterServices;
    
        public PoSegmentParameterController(IPoSegmentParameterServices PoSegmentParameterServices)
        {
            _poSegmentParameterServices = PoSegmentParameterServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<PoSegmentParameterEntity>>> GetList([FromBody] PoSegmentParameterRequestModel reqModel)
        {
            var data = await _poSegmentParameterServices.GetList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<PoSegmentParameterEntity>>> GetPoSegmentParameterList([FromBody] PoSegmentParameterRequestModel reqModel)
        {
            var data = await _poSegmentParameterServices.GetPoSegmentParameterList(reqModel);
            return Success(data, "获取成功");
        }
     
        [HttpPost]
        public async Task<MessageModel<PageModel<PoSegmentParameterEntity>>> GetPageList([FromBody] PoSegmentParameterRequestModel reqModel)
        {
            var data = await _poSegmentParameterServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoSegmentParameterEntity>> GetEntity(string id)
        {
            var data = await _poSegmentParameterServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoSegmentParameterEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _poSegmentParameterServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                var entity = await _poSegmentParameterServices.FindEntity(request.ID);
                entity.ParameterValue = request.ParameterValue;
                data.success = await _poSegmentParameterServices.Update(entity);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] PoSegmentParameterEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poSegmentParameterServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poSegmentParameterServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class PoSegmentParameterRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IDicMaterialPreselectViewServices
	/// </summary>	
    public interface IDicMaterialPreselectViewServices :IBaseServices<DicMaterialPreselectViewEntity>
	{
		Task<PageModel<DicMaterialPreselectViewEntity>> GetPageList(DicMaterialPreselectViewRequestModel reqModel);

        Task<List<DicMaterialPreselectViewEntity>> GetList(DicMaterialPreselectViewRequestModel reqModel);

		Task<bool> SaveForm(DicMaterialPreselectViewEntity entity);
    }
}
using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PTM_B_DCS_OP_STATUS")] 
    public class DcsOpStatusEntity : EntityBase
    {
        public DcsOpStatusEntity()
        {
        }
           /// <summary>
           /// Desc:工单
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:设备
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:OPID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OP_ID")]
        public string OpId { get; set; }
           /// <summary>
           /// Desc:Op名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OP_NAME")]
        public string OpName { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATE")]
        public string State { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

    }
}
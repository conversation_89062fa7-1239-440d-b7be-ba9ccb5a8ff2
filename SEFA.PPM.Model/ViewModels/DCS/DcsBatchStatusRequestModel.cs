using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class DcsBatchStatusRequestModel : RequestPageModelBase
    {
        public DcsBatchStatusRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
        public string State { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }

    }
}
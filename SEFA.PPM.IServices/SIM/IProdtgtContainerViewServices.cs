using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IProdtgtContainerViewServices
	/// </summary>	
    public interface IProdtgtContainerViewServices :IBaseServices<ProdtgtContainerViewEntity>
	{
		Task<PageModel<ProdtgtContainerViewEntity>> GetPageList(ProdtgtContainerViewRequestModel reqModel);

        Task<List<ProdtgtContainerViewEntity>> GetList(ProdtgtContainerViewRequestModel reqModel);

		Task<bool> SaveForm(ProdtgtContainerViewEntity entity);
    }
}
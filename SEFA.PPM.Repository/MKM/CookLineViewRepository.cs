using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// CookLineViewRepository
	/// </summary>
    public class CookLineViewRepository : BaseRepository<CookLineViewEntity>, ICookLineViewRepository
    {
        public CookLineViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}
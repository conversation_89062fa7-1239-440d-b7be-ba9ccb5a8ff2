using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PTM_B_DCS_BATCH_STATUS")] 
    public class DcsBatchStatusEntity : EntityBase
    {
        public DcsBatchStatusEntity()
        {
        }
           /// <summary>
           /// Desc:工单
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATE")]
        public string State { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

    }
}
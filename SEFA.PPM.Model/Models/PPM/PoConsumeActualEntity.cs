using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PPM_B_PO_CONSUME_ACTUAL")]
	public class PoConsumeActualEntity : EntityBase
	{
		public PoConsumeActualEntity()
		{
		}
		/// <summary>
		/// Desc:订单ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
		/// <summary>
		/// Desc:订单工序消耗需求ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PO_CONSUME_REQUIREMENT_ID")]
		public string PoConsumeRequirementId { get; set; }
		/// <summary>
		/// Desc:生产执行记录ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCT_EXECUTION_ID")]
		public string ProductExecutionId { get; set; }
		/// <summary>
		/// Desc:设备ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_ID")]
		public string EquipmentId { get; set; }
		/// <summary>
		/// Desc:资源存储区ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SOURCE_EQUIPMENT_ID")]
		public string SourceEquipmentId { get; set; }
		/// <summary>
		/// Desc:数量
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "QUANTITY")]
		public decimal? Quantity { get; set; }
		/// <summary>
		/// Desc:计量单位ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "UNIT_ID")]
		public string UnitId { get; set; }
		/// <summary>
		/// Desc:批次ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LOT_ID")]
		public string LotId { get; set; }
		/// <summary>
		/// Desc:二级批次ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SUB_LOT_ID")]
		public string SubLotId { get; set; }
		/// <summary>
		/// Desc:二级批次状态
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SUB_LOT_STATUS")]
		public int? SubLotStatus { get; set; }
		/// <summary>
		/// Desc:存储仓库
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "STORAGE_BIN")]
		public string StorageBin { get; set; }
		/// <summary>
		/// Desc:存储仓库库位
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "STORAGE_LOCATION")]
		public string StorageLocation { get; set; }
		/// <summary>
		/// Desc:容器ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONTAINER_ID")]
		public string ContainerId { get; set; }
		/// <summary>
		/// Desc:班组ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TEAM_ID")]
		public string TeamId { get; set; }
		/// <summary>
		/// Desc:班次ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SHIFT_ID")]
		public string ShiftId { get; set; }
		/// <summary>
		/// Desc:原因代码
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REASON_CODE")]
		public string ReasonCode { get; set; }
		/// <summary>
		/// Desc:意见
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "COMMENT")]
		public string Comment { get; set; }
		/// <summary>
		/// Desc:发送状态0未发送，1已发送，2发送失败，3未发送（手动下发，不判断月结报工状态）,4已发送待SAP回调
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SEND_EXTERNAL")]
		public int SendExternal { get; set; }
		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "DELETED")]
		public int? Deleted { get; set; }
		/// <summary>
		/// Desc:批次状态
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LOT_STATUS")]
		public string LotStatus { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public string Type { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MJAHR")]
		public string Mjahr { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MBLNR")]
		public string Mblnr { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "ZEILE")]
		public string Zeile { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG")]
		public string Msg { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PID")]
		public string Pid { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TRAN_NO")]
		public string TranNo { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "INDEX")]
		public int? Index { get; set; }
		/// <summary>
		/// Desc:发送时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SEND_TIME")]
		public DateTime? SendTime { get; set; }
	}
}
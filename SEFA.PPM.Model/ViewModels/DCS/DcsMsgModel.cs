using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class DcsMsgModel
    {
        public DcsMsgModel()
        {
        }
        /// <summary>
        /// Desc:工单
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchId { get; set; }
        /// <summary>
        /// Desc:设备
        /// Default:
        /// Nullable:False
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:OPID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string OpId { get; set; }
        /// <summary>
        /// Desc:Op名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string OpName { get; set; }
        /// <summary>
        /// Desc:投料口
        /// Default:
        /// Nullable:False
        /// </summary>
        public string InputMaterialEquCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:储罐号
        /// Default:
        /// Nullable:False
        /// </summary>
        public string StorageTank { get; set; }
        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string State { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Desc:信息
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Desc:时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime MsgTime { get; set; }

    }
}
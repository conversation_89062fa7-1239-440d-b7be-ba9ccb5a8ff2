using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface.WMS;

public class CallMaterialSheet
{
    /// <summary>
    /// 叫料申请单
    /// </summary>
    public string RequestSheetNo { get; set; }
    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime RequestTime { get; set; }
    /// <summary>
    /// 产线编码（与库位码二选一必须传值）
    /// </summary>
    public string LineCode { get; set; }
    /// <summary>
    /// 库位码（与产线编码二选一必须传值）
    /// </summary>
    public string PositionCode { get; set; }
    /// <summary>
    /// 操作类型，1：叫料，-1：取消叫料
    /// </summary>
    public int OperationType { get; set; }
    /// <summary>
    /// 叫料类型，正常：MATERIAL，IBC空桶:EMPTY_IBC
    /// </summary>
    public string RequestType { get; set; }
    /// <summary>
    /// 请求明细列表
    /// </summary>
    public List<CallMaterialDetail> RequestDetailsList { get; set; }
}
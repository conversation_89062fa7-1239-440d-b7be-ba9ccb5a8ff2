using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class CallMaterialDetailController : BaseApiController
    {
        /// <summary>
        /// CallMaterialDetail
        /// </summary>
        private readonly ICallMaterialDetailServices _callMaterialDetailServices;

        public CallMaterialDetailController(ICallMaterialDetailServices CallMaterialDetailServices)
        {
            _callMaterialDetailServices = CallMaterialDetailServices;
        }

        /// <summary>
        /// 获取叫料单明细列表
        /// </summary>
        /// <param name="reqModel">查询条件</param>
        /// <returns>叫料单明细列表</returns>
        [HttpPost]
        public async Task<MessageModel<List<CallMaterialDetailEntity>>> GetList(
            [FromBody] CallMaterialDetailRequestModel reqModel)
        {
            var data = await _callMaterialDetailServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取叫料单明细分页列表
        /// </summary>
        /// <param name="reqModel">查询条件</param>
        /// <returns>叫料单明细分页列表</returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<CallMaterialDetailEntity>>> GetPageList(
            [FromBody] CallMaterialDetailRequestModel reqModel)
        {
            var data = await _callMaterialDetailServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据ID获取叫料单明细信息
        /// </summary>
        /// <param name="id">叫料单明细ID</param>
        /// <returns>叫料单明细信息</returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<CallMaterialDetailEntity>> GetEntity(string id)
        {
            var data = await _callMaterialDetailServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 保存叫料单明细信息
        /// </summary>
        /// <param name="request">叫料单明细信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] CallMaterialDetailEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _callMaterialDetailServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _callMaterialDetailServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        /// <summary>
        /// 新增叫料单明细信息
        /// </summary>
        /// <param name="request">叫料单明细信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] CallMaterialDetailEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _callMaterialDetailServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        /// <summary>
        /// 删除叫料单明细信息
        /// </summary>
        /// <param name="ids">叫料单明细ID数组</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _callMaterialDetailServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        
        /// <summary>
        /// 根据主表ID获取所有子表记录
        /// </summary>
        /// <param name="headerId">主表ID</param>
        /// <returns>叫料单明细列表</returns>
        [HttpGet("{headerId}/by-header")]
        public async Task<MessageModel<List<CallMaterialDetailEntity>>> GetDetailsByHeaderId(string headerId)
        {
            var result = await _callMaterialDetailServices.GetDetailsByHeaderId(headerId);
            if (result.success)
            {
                return Success(result.response, result.msg);
            }
            else
            {
                return Failed<List<CallMaterialDetailEntity>>(result.msg);
            }
        }
        
        /// <summary>
        /// 批量保存子表记录
        /// </summary>
        /// <param name="request">批量保存请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("batch-save")]
        public async Task<MessageModel<string>> BatchSaveDetails([FromBody] BatchSaveDetailsRequest request)
        {
            var result = await _callMaterialDetailServices.SaveDetails(request.HeaderId, request.Details);
            if (result.success)
            {
                return Success("", result.msg);
            }
            else
            {
                return Failed(result.msg);
            }
        }
    }
    
    /// <summary>
    /// 批量保存明细请求
    /// </summary>
    public class BatchSaveDetailsRequest
    {
        /// <summary>
        /// 主表ID
        /// </summary>
        public string HeaderId { get; set; }
        
        /// <summary>
        /// 明细列表
        /// </summary>
        public List<CallMaterialDetailEntity> Details { get; set; }
    }
}
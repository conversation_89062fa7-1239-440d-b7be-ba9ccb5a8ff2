namespace SEFA.PPM.Model.Models.Interface.WMS;

public class DistributionMaterialDetail
{
    /// <summary>
    /// Plant
    /// </summary>
    public string Plant { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    public string MaterialCode { get; set; }

    /// <summary>
    /// 物料描述
    /// </summary>
    public string MaterialName { get; set; }

    /// <summary>
    /// 物料版本号
    /// </summary>
    public string MaterialVersionCode { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 托盘号
    /// </summary>
    public string PalletNo { get; set; }

    /// <summary>
    /// 物料唯一标签码
    /// </summary>
    public string BarCode { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public double Quantity { get; set; }

    /// <summary>
    /// 计量单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 密度
    /// </summary>
    public double Density { get; set; }

    /// <summary>
    /// Coa含量%
    /// </summary>
    public double CoAContent { get; set; }

    /// <summary>
    /// 备注说明
    /// </summary>
    public string Remark { get; set; }
}
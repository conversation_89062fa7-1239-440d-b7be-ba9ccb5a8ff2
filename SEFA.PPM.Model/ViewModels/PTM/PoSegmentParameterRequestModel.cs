using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class PoSegmentParameterRequestModel : RequestPageModelBase
    {
        public PoSegmentParameterRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:产品工序ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SapSegmentMaterialId { get; set; }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:关键参数名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ParameterName { get; set; }
           /// <summary>
           /// Desc:关键参数值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ParameterValue { get; set; }
           /// <summary>
           /// Desc:数据块
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DataBlock { get; set; }
           /// <summary>
           /// Desc:数据块点位
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DataBlockItem { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }

    }
}
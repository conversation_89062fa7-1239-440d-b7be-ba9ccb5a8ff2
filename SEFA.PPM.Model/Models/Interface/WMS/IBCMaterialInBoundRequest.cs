using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface.WMS;

/// <summary>
/// IBC桶配置成品入库申请请求参数
/// </summary>
public class IBCMaterialInBoundRequest
{
    /// <summary>
    /// 当前位置
    /// </summary>
    public string PoistionCode { get; set; }

    /// <summary>
    /// 退库仓库，默认是立库
    /// </summary>
    public string WarehouseCode { get; set; }

    /// <summary>
    /// IBC桶唯一追溯码
    /// </summary>
    public string IBCNo { get; set; }

    /// <summary>
    /// 入库明细列表
    /// </summary>
    public List<IBCMaterialInBoundDetail> DetailsList { get; set; }
}
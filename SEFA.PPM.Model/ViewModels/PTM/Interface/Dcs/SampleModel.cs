using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using System.Collections.Generic;

namespace SEFA.PPM.Model.ViewModels.PTM
{
    public class SampleRequestModel : SampleCommonModel
    {
        /// <summary>
        /// 信息，取样口 or 允许取样
        /// </summary>
        public string Message { get; set; }
    }

    public class SampleModel : SampleCommonModel
    {
        /// <summary>
        /// 取样口
        /// </summary>
        public string SamplingPort { get; set; }

        /// <summary>
        /// 取样标识 开始1，结束2
        /// </summary>
        public int SampleFlag { get; set; } = 0;
    }

    public class SampleResultModel : SampleCommonModel
    {
        /// <summary>
        /// 质检结果 “OK” :合格，"NG"：失败
        /// </summary>
        public string SampleResult { get; set; }

        /// <summary>
        /// 取样结果
        /// </summary>
        public List<SampleParameterModel> SampleParameterList { get; set; }
    }

    public class SampleCommonModel
    {

        /// <summary>
        /// 【工单号】
        /// </summary>
        public string BatchId { get; set; }

        /// <summary>
        /// 【设备号】
        /// </summary>
        public string UnitId { get; set; }

        /// <summary>
        /// 【OperationId】
        /// </summary>
        public string OpId { get; set; }

        /// <summary>
        /// 【OperationName】
        /// </summary>
        public string OpName { get; set; }

      
    }

    /// <summary>
    /// 质检参数
    /// </summary>
    public class SampleParameterModel
    {
        /// <summary>
        /// 质检参数名称
        /// </summary>
        public string ParameterName { get; set; }

        /// <summary>
        /// 质检参数点位
        /// </summary>
        public string ParameterNameTag { get; set; }

        /// <summary>
        /// 质检参数值
        /// </summary>
        public string ParameterValue { get; set; }

        /// <summary>
        /// 质检参数值点位
        /// </summary>
        public string ParameterValueTag { get; set; }

    }

}
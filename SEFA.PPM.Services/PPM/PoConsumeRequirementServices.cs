using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.ViewModels.PPM;
using System;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.UnitOfWork;

namespace SEFA.PPM.Services
{
    public class PoConsumeRequirementServices : BaseServices<PoConsumeRequirementEntity>, IPoConsumeRequirementServices
    {
        #region 常量定义

        /// <summary>
        /// 备料状态常量
        /// </summary>
        private static class PreparationStatus
        {
            /// <summary>
            /// 无需备料
            /// </summary>
            public const string NoPreparation = "1";

            /// <summary>
            /// 待备料
            /// </summary>
            public const string WaitingPreparation = "2";

            /// <summary>
            /// 备料中
            /// </summary>
            public const string InPreparation = "12";

            /// <summary>
            /// 备料完成
            /// </summary>
            public const string PreparationCompleted = "3";

            /// <summary>
            /// 投料中
            /// </summary>
            public const string InFeeding = "8";

            /// <summary>
            /// 投料完成
            /// </summary>
            public const string FeedingCompleted = "9";
        }

        /// <summary>
        /// SAP需求标识常量
        /// </summary>
        private static class SapRequirementFlags
        {
            /// <summary>
            /// 需要准备
            /// </summary>
            public const string NeedPrepare = "1";

            /// <summary>
            /// 不需要准备
            /// </summary>
            public const string NoNeedPrepare = "0";

            /// <summary>
            /// 需要提前准备
            /// </summary>
            public const string NeedAdvancePrepare = "1";

            /// <summary>
            /// 不需要提前准备
            /// </summary>
            public const string NoNeedAdvancePrepare = "0";
        }

        #endregion

        private readonly IBaseRepository<PoConsumeRequirementEntity> _dal;
        private readonly IBaseRepository<BatchConsumeRequirementEntity> _batchDal;
        private readonly IUser _user;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;

        public PoConsumeRequirementServices(IBaseRepository<PoConsumeRequirementEntity> dal,
            IBaseRepository<BatchConsumeRequirementEntity> batchDal,
            IUser user,
            IMapper mapper,
            IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._batchDal = batchDal;
            this._user = user;
            this._mapper = mapper;
            this._unitOfWork = unitOfWork;
        }

        public async Task<List<PoConsumeRequirementEntity>> GetList(PoConsumeRequirementRequestModel reqModel)
        {
            List<PoConsumeRequirementEntity> result = new List<PoConsumeRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<PoConsumeRequirementEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<PoConsumeRequirementEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<PoConsumeRequirementEntity>> GetPageList(PoConsumeRequirementRequestModel reqModel)
        {
            PageModel<PoConsumeRequirementEntity> result = new PageModel<PoConsumeRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<PoConsumeRequirementEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<PoConsumeRequirementEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(PoConsumeRequirementEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 根据工单编码、设备编码、物料编码、物料需求数量生成工单物料需求单
        /// </summary>
        /// <param name="request">包含工单、工序、物料等信息的请求模型</param>
        /// <returns>操作结果消息模型</returns>
        public async Task<MessageModel<BatchConsumeRequirementEntity>> GeneratePoConsumeRequirement(
            PoConsumeRequirementViewModel request)
        {
            var logFileName = $"DCS调用生成物料需求计划接口日志_{DateTime.Now.ToString("yyyyMMdd")}.log";
            // 参数验证
            if (string.IsNullOrWhiteSpace(request.OrderCode))
                return MessageModel<BatchConsumeRequirementEntity>.Fail("工单编码不能为空");
            if (string.IsNullOrWhiteSpace(request.SegmentId))
                return MessageModel<BatchConsumeRequirementEntity>.Fail("工序编码不能为空");
            if (string.IsNullOrWhiteSpace(request.MaterialCode))
                return MessageModel<BatchConsumeRequirementEntity>.Fail("物料编码不能为空");
            if (request.Quantity <= 0)
                return MessageModel<BatchConsumeRequirementEntity>.Fail("数量不能小于等于0");

            // 查询物料需求
            var requirements = await QueryRequirements(request);
            if (requirements.Count == 0)
                return MessageModel<BatchConsumeRequirementEntity>.Fail("没有找到对应的物料需求，请核对工单、工序、物料编码是否正确");

            // 处理多个需求
            return await HandleMultipleRequirements(requirements, request, logFileName);
        }

        /// <summary>
        /// 创建新的物料需求
        /// </summary>
        /// <param name="requirements">匹配的物料需求List</param>
        /// <param name="request">DCS请求参数</param>
        /// <param name="logFileName">日志名称</param>
        /// <returns></returns>
        private async Task<MessageModel<BatchConsumeRequirementEntity>> HandleMultipleRequirements(
            List<PoConsumeRequiretAllViewModel> requirements,
            PoConsumeRequirementViewModel request,
            string logFileName)
        {
            var requirementDefault = requirements.FirstOrDefault();
            var materialInfo = requirementDefault?.MaterialStep;
            var consumeRequirement = requirementDefault?.PoConsumeRequirement;
            var batchRequirement = requirementDefault?.BatchConsumeRequirement;
            if (materialInfo == null)
                return MessageModel<BatchConsumeRequirementEntity>.Fail("没有找到对应的物料信息");

            if (consumeRequirement == null || batchRequirement == null)
                return MessageModel<BatchConsumeRequirementEntity>.Fail("没有找到对应的物料需求");

            //不需要备料且需要提前备料-异常情况
            if (materialInfo.NeedPrepare == "0" && materialInfo.NeedAdvancePrepare == "1")
            {
                return MessageModel<BatchConsumeRequirementEntity>.Fail("当前物料需求不需要备料，但却需要提前备料，请联系管理员修改物料备料属性！");
            }

            //需要备料且需提前备料-不可直接备料
            if (materialInfo.NeedPrepare == "1" && materialInfo.NeedAdvancePrepare == "1")
            {
                return MessageModel<BatchConsumeRequirementEntity>.Fail("当前物料需要提前备料，不能直接备料");
            }

            #region 1.不需要备料且不需要提前备料2.需要备料且不需要提前备料

            var prepareStatusList = requirements.Select(a => a.BatchConsumeRequirement.PreparationStatus).Distinct()
                .ToList();
            //存在已备料或者投料中的物料需求时，不能再次备料
            if (prepareStatusList.Contains(PreparationStatus.PreparationCompleted) ||
                prepareStatusList.Contains(PreparationStatus.InFeeding))
            {
                return MessageModel<BatchConsumeRequirementEntity>.Fail("当前物料需求已备料，不能再次备料");
            }

            //存在待备料物料需求时更新待备料需求的数量
            if (prepareStatusList.Contains(PreparationStatus.WaitingPreparation))
            {
                //存在待备料物料需求时更新待备料需求的数量
                var waitingRequirements = requirements.Where(a =>
                    a.PoConsumeRequirement.PreparationStatus == PreparationStatus.WaitingPreparation).ToList();
                if (waitingRequirements.Count > 1)
                {
                    SerilogServer.LogDebug(
                        $"当前物料需求【存在待备料物料场景-出现多个待备料需求】-[{JsonConvert.SerializeObject(request)}]存在多个待备料需求,无法处理。",
                        logFileName);
                    return MessageModel<BatchConsumeRequirementEntity>.Fail("当前物料需求存在多个待备料需求，请联系管理员查找原因");
                }

                var waitingRequirement = waitingRequirements.FirstOrDefault()?.BatchConsumeRequirement;
                var waitingConsume = waitingRequirements.FirstOrDefault()?.PoConsumeRequirement;
                if (waitingConsume == null || waitingRequirement == null)
                {
                    SerilogServer.LogDebug(
                        $"当前物料需求【存在待备料物料场景-未找到待备料需求】-[{JsonConvert.SerializeObject(request)}]查找待备料物料需求存在问题,无法处理。",
                        logFileName);
                    return MessageModel<BatchConsumeRequirementEntity>.Fail("当前物料需求查找待备料物料需求存在问题，请联系管理员查找原因");
                }

                waitingRequirement.Quantity = request.Quantity;
                waitingRequirement.WeighingQty = request.Quantity;
                waitingConsume.Quantity = request.Quantity;
                waitingConsume.AdjustPercentQuantity = request.Quantity;
                waitingConsume.WeighingQty = request.Quantity;
                return await UpdateRequirement(waitingRequirement, waitingConsume, "更新物料需求");
            }

            //创建新的物料需求
            var newEntity = CreateNewRequirement(consumeRequirement, request);
            return await AddRequirement(newEntity, batchRequirement.BatchId, "新增物料需求");

            #endregion
        }


        /// <summary>
        /// 查询物料需求信息
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>物料需求视图模型列表</returns>
        private async Task<List<PoConsumeRequiretAllViewModel>> QueryRequirements(PoConsumeRequirementViewModel request)
        {
            return await _dal.Db
                .Queryable<BatchConsumeRequirementEntity, PoConsumeRequirementEntity, PoSegmentRequirementEntity,
                    SapSegmentMaterialStepEntity, SapSegmentMaterialEntity, SapSegmentEntity,
                    ProductionOrderEntity, MaterialEntity>((t7, t, t1, t2, t3, t4, t5, t6) => new object[]
                {
                    JoinType.Left, t7.PoConsumeRequirementId == t.ID,
                    JoinType.Left, t.PoSegmentRequirementId == t1.ID,
                    JoinType.Left, t.MaterialId == t2.MaterialId,
                    JoinType.Left, t2.SapSegmentMaterialId == t3.ID,
                    JoinType.Left, t3.SapSegmentId == t4.ID && t1.SegmentId == t4.ID,
                    JoinType.Left, t3.MaterialVersionId == t5.MaterialVersionId && t.ProductionOrderId == t5.ID,
                    JoinType.Left, t2.MaterialId == t6.ID
                })
                .WhereIF(!string.IsNullOrWhiteSpace(request.MaterialVersionId),
                    (t7, t, t1, t2, t3, t4, t5, t6) => t2.MaterialVersionId == request.MaterialVersionId)
                .Where((t7, t, t1, t2, t3, t4, t5, t6) =>
                    t5.ProductionOrderNo == request.OrderCode &&
                    t6.Code == request.MaterialCode &&
                    t4.ID == request.SegmentId)
                .Select((t7, t, t1, t2, t3, t4, t5, t6) => new PoConsumeRequiretAllViewModel
                {
                    BatchConsumeRequirement = t7,
                    PoConsumeRequirement = t,
                    MaterialStep = t2,
                }).ToListAsync();
        }

        /// <summary>
        /// 创建新的物料需求实体
        /// </summary>
        /// <param name="source">源实体</param>
        /// <param name="request">请求参数</param>
        /// <returns>新的物料需求实体</returns>
        private PoConsumeRequirementEntity CreateNewRequirement(
            PoConsumeRequirementEntity source,
            PoConsumeRequirementViewModel request)
        {
            var newEntity = _mapper.Map<PoConsumeRequirementEntity>(source);
            newEntity.CreateCustomGuid(_user.UserName);
            newEntity.AdjustPercentQuantity = request.Quantity;
            newEntity.CurrentFlag = "1";
            newEntity.PreparationStatus = PreparationStatus.WaitingPreparation;
            return newEntity;
        }

        /// <summary>
        /// 更新物料需求
        /// </summary>
        /// <param name="entity">物料需求实体</param>
        /// <param name="successMessage">成功消息</param>
        /// <returns>操作结果</returns>
        private async Task<MessageModel<BatchConsumeRequirementEntity>> UpdateRequirement(
            BatchConsumeRequirementEntity entity,
            PoConsumeRequirementEntity consumeRequirement,
            string successMessage)
        {
            var success = false;
            _unitOfWork.BeginTran();
            try
            {
                await _batchDal.Update(entity);
                await _dal.Update(consumeRequirement);
                _unitOfWork.CommitTran();
                success = true;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                throw ex;
            }

            return new MessageModel<BatchConsumeRequirementEntity>
            {
                success = success,
                msg = success ? $"{successMessage}成功！" : $"{successMessage}失败！",
                response = entity
            };
        }


        /// <summary>
        /// 添加物料需求
        /// </summary>
        /// <param name="entity">物料需求实体</param>
        /// <param name="successMessage">成功消息</param>
        /// <returns>操作结果</returns>
        private async Task<MessageModel<BatchConsumeRequirementEntity>> AddRequirement(
            PoConsumeRequirementEntity entity, string batchId,
            string successMessage)
        {
            var success = false;
            BatchConsumeRequirementEntity batchEntity = null;

            _unitOfWork.BeginTran();
            try
            {
                // 添加物料需求
                var requirementSuccess = await _dal.Add(entity) > 0;

                if (requirementSuccess)
                {
                    // 创建对应的批次物料需求
                    batchEntity = new BatchConsumeRequirementEntity
                    {
                        BatchId = batchId,
                        PoConsumeRequirementId = entity.ID,
                        Quantity = entity.Quantity ?? 0,
                        WeighingQty = entity.WeighingQty,
                        FeedStates = 0,
                        ChangeUnit = entity.ChangeUnit,
                        PreparationStatus = entity.PreparationStatus,
                        BomMaterialId = entity.BomMaterialId,
                        CurrentFlag = entity.CurrentFlag
                    };
                    batchEntity.CreateCustomGuid(_user.UserName);

                    success = await _batchDal.Add(batchEntity) > 0;
                }

                if (success)
                {
                    _unitOfWork.CommitTran();
                }
                else
                {
                    _unitOfWork.RollbackTran();
                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                throw ex;
            }

            return new MessageModel<BatchConsumeRequirementEntity>
            {
                success = success,
                msg = success ? $"{successMessage}成功！" : $"{successMessage}失败！",
                response = batchEntity
            };
        }
    }
}
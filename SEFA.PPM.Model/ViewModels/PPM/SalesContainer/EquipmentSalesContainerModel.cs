using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using Magicodes.ExporterAndImporter.Core;

namespace SEFA.PPM.Model.ViewModels
{
    public class EquipmentSalesContainerModel :EntityBase
    {
        /// <summary>
        /// Desc:产线Id
        /// Default:
        /// Nullable:False
        /// </summary>

        public string EquipmentId { get; set; } 
        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string EquipmentCode { get; set; }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:规格Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SalesContainerId { get; set; }
        /// <summary>
        /// Desc:规格代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SalesContainerCode { get; set; }
        /// <summary>
        /// Desc:规格描述
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SalesContainerName { get; set; }


    }
}